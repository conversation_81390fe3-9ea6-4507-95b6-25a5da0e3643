import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![file-excel](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC43YzYgNiA5LjQgMTQuMSA5LjQgMjIuNlY5MjhjMCAxNy43LTE0LjMgMzItMzIgMzJIMTkyYy0xNy43IDAtMzItMTQuMy0zMi0zMlY5NmMwLTE3LjcgMTQuMy0zMiAzMi0zMmg0MjQuN2M4LjUgMCAxNi43IDMuNCAyMi43IDkuNGwyMTUuMiAyMTUuM3pNNzkwLjIgMzI2TDYwMiAxMzcuOFYzMjZoMTg4LjJ6TTU3NS4zNCA0NzcuODRsLTYxLjIyIDEwMi4zTDQ1Mi4zIDQ3Ny44YTEyIDEyIDAgMDAtMTAuMjctNS43OWgtMzguNDRhMTIgMTIgMCAwMC02LjQgMS44NSAxMiAxMiAwIDAwLTMuNzUgMTYuNTZsODIuMzQgMTMwLjQyLTgzLjQ1IDEzMi43OGExMiAxMiAwIDAwLTEuODQgNi4zOSAxMiAxMiAwIDAwMTIgMTJoMzQuNDZhMTIgMTIgMCAwMDEwLjIxLTUuN2w2Mi43LTEwMS40NyA2Mi4zIDEwMS40NWExMiAxMiAwIDAwMTAuMjMgNS43MmgzNy40OGExMiAxMiAwIDAwNi40OC0xLjkgMTIgMTIgMCAwMDMuNjItMTYuNThsLTgzLjgzLTEzMC41NSA4NS4zLTEzMi40N2ExMiAxMiAwIDAwMS45LTYuNSAxMiAxMiAwIDAwLTEyLTEyaC0zNS43YTEyIDEyIDAgMDAtMTAuMjkgNS44NHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
