<?php
// 测试store_code API接口

echo "<h2>测试store_code API接口</h2>";
echo "<p>验证修改后的API是否正常工作...</p>";

// 测试数据
$test_order = [
    'store_code' => 'STORE001',
    'customer_name' => '测试用户',
    'customer_phone' => '13800138000',
    'dining_type' => 'dine_in',
    'table_number' => 'A01',
    'items' => [
        [
            'dish_id' => 1,
            'quantity' => 2,
            'special_requirements' => '不要辣'
        ]
    ]
];

echo "<h3>📝 测试数据</h3>";
echo "<pre style='background-color: #f5f5f5; padding: 10px; border-radius: 3px;'>";
echo json_encode($test_order, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
echo "</pre>";

echo "<h3>🔧 API调用</h3>";
echo "<p>正在调用 POST /api/mobile/orders...</p>";

// 发送POST请求
$url = 'http://localhost:8000/api/mobile/orders';
$data = json_encode($test_order);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($data)
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

echo "<h3>📊 API响应</h3>";
echo "<p><strong>HTTP状态码:</strong> {$http_code}</p>";

if ($curl_error) {
    echo "<p style='color: red;'><strong>CURL错误:</strong> {$curl_error}</p>";
} else {
    echo "<p style='color: green;'>✅ 请求发送成功</p>";
}

if ($response) {
    echo "<h4>响应内容:</h4>";
    echo "<pre style='background-color: #f5f5f5; padding: 10px; border-radius: 3px; max-height: 400px; overflow-y: auto;'>";
    
    // 尝试格式化JSON响应
    $json_response = json_decode($response, true);
    if ($json_response) {
        echo json_encode($json_response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        
        // 分析响应
        echo "</pre>";
        echo "<h4>📋 响应分析:</h4>";
        if (isset($json_response['code'])) {
            $code = $json_response['code'];
            if ($code == 200) {
                echo "<p style='color: green;'>✅ API调用成功 (code: {$code})</p>";
                if (isset($json_response['data']['order_id'])) {
                    $order_id = $json_response['data']['order_id'];
                    echo "<p>📦 订单ID: {$order_id}</p>";
                    
                    // 查询数据库验证数据
                    echo "<h4>🔍 数据库验证:</h4>";
                    try {
                        $pdo = new PDO('mysql:host=localhost;dbname=orderflow;charset=utf8mb4', 'root', 'root');
                        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                        
                        $stmt = $pdo->prepare("SELECT order_no, customer_name, store_code, store_name, total_amount, created_at FROM restaurant_orders WHERE id = ?");
                        $stmt->execute([$order_id]);
                        $order_data = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        if ($order_data) {
                            echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
                            echo "<tr style='background-color: #f0f0f0;'><th>字段</th><th>值</th></tr>";
                            foreach ($order_data as $key => $value) {
                                $bgColor = in_array($key, ['store_code', 'store_name']) ? 'background-color: #e6ffe6;' : '';
                                echo "<tr style='{$bgColor}'>";
                                echo "<td><strong>{$key}</strong></td>";
                                echo "<td>{$value}</td>";
                                echo "</tr>";
                            }
                            echo "</table>";
                            
                            if ($order_data['store_code'] === 'STORE001' && !empty($order_data['store_name'])) {
                                echo "<p style='color: green;'>✅ store_code和store_name字段正确保存</p>";
                            } else {
                                echo "<p style='color: orange;'>⚠️ store_code或store_name字段可能有问题</p>";
                            }
                        } else {
                            echo "<p style='color: red;'>❌ 在数据库中未找到订单记录</p>";
                        }
                    } catch (Exception $e) {
                        echo "<p style='color: red;'>❌ 数据库查询失败: " . $e->getMessage() . "</p>";
                    }
                }
            } else {
                echo "<p style='color: red;'>❌ API调用失败 (code: {$code})</p>";
                if (isset($json_response['message'])) {
                    echo "<p><strong>错误信息:</strong> {$json_response['message']}</p>";
                }
            }
        }
    } else {
        echo htmlspecialchars($response);
        echo "</pre>";
        echo "<p style='color: orange;'>⚠️ 响应不是有效的JSON格式</p>";
    }
} else {
    echo "<p style='color: red;'>❌ 没有收到响应</p>";
}

echo "<h3>🏪 可用门店列表</h3>";
try {
    $pdo = new PDO('mysql:host=localhost;dbname=orderflow;charset=utf8mb4', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("SELECT code, name, address, status FROM store_info ORDER BY code");
    $stores = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($stores) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'><th>门店编号</th><th>门店名称</th><th>地址</th><th>状态</th></tr>";
        foreach ($stores as $store) {
            $statusText = $store['status'] == 1 ? '启用' : '禁用';
            $statusColor = $store['status'] == 1 ? 'green' : 'red';
            echo "<tr>";
            echo "<td style='font-family: monospace; font-weight: bold;'>{$store['code']}</td>";
            echo "<td>{$store['name']}</td>";
            echo "<td>{$store['address']}</td>";
            echo "<td style='color: {$statusColor};'>{$statusText}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 查询门店列表失败: " . $e->getMessage() . "</p>";
}

echo "<h3>📝 使用说明</h3>";
echo "<div style='background-color: #f0f8ff; padding: 15px; border-radius: 5px;'>";
echo "<h4>✅ 修改完成的内容:</h4>";
echo "<ul>";
echo "<li>数据库字段: shop_code → store_code, shop_name → store_name</li>";
echo "<li>API参数: shop_code → store_code</li>";
echo "<li>后端逻辑: 使用store_code查询门店信息</li>";
echo "</ul>";
echo "<h4>📱 新的API调用方式:</h4>";
echo "<pre style='background-color: #f5f5f5; padding: 10px; border-radius: 3px;'>";
echo "POST /api/mobile/orders\n";
echo "{\n";
echo "  \"store_code\": \"STORE001\",  // 使用store_code\n";
echo "  \"customer_name\": \"张三\",\n";
echo "  \"customer_phone\": \"13800138000\",\n";
echo "  \"items\": [...]\n";
echo "}";
echo "</pre>";
echo "</div>";
?>
