import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![file-ppt](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ2NC41IDUxNi4ydjEwOC40aDM4LjljNDQuNyAwIDcxLjItMTAuOSA3MS4yLTU0LjMgMC0zNC40LTIwLjEtNTQuMS01My45LTU0LjFoLTU2LjJ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik01MzQgMzUyVjEzNkgyMzJ2NzUyaDU2MFYzOTRINTc2YTQyIDQyIDAgMDEtNDItNDJ6bTkwIDIxOC40YzAgNTUuMi0zNi44IDk0LjEtOTYuMiA5NC4xaC02My4zVjc2MGMwIDQuNC0zLjYgOC04IDhINDI0Yy00LjQgMC04LTMuNi04LThWNDg0YzAtNC40IDMuNi04IDgtOHYuMWgxMDRjNTkuNyAwIDk2IDM5LjggOTYgOTQuM3oiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTg1NC42IDI4OC42TDYzOS40IDczLjRjLTYtNi0xNC4xLTkuNC0yMi42LTkuNEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzExLjNjMC04LjUtMy40LTE2LjctOS40LTIyLjd6TTYwMiAxMzcuOEw3OTAuMiAzMjZINjAyVjEzNy44ek03OTIgODg4SDIzMlYxMzZoMzAydjIxNmE0MiA0MiAwIDAwNDIgNDJoMjE2djQ5NHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTQyNCA0NzYuMWMtNC40LS4xLTggMy41LTggNy45djI3NmMwIDQuNCAzLjYgOCA4IDhoMzIuNWM0LjQgMCA4LTMuNiA4LTh2LTk1LjVoNjMuM2M1OS40IDAgOTYuMi0zOC45IDk2LjItOTQuMSAwLTU0LjUtMzYuMy05NC4zLTk2LTk0LjNINDI0em0xNTAuNiA5NC4yYzAgNDMuNC0yNi41IDU0LjMtNzEuMiA1NC4zaC0zOC45VjUxNi4yaDU2LjJjMzMuOCAwIDUzLjkgMTkuNyA1My45IDU0LjF6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
