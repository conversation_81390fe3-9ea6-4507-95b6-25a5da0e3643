<?php
namespace app\controller;

use think\facade\Db;
use think\facade\Log;
use think\Request;

/**
 * 阿奇索订单推送接收控制器
 */
class AgisoWebhookController extends BaseController
{
    // 阿奇索AppSecret，实际使用时请从配置文件读取
    private $appSecret = 'your_app_secret_here';
    
    /**
     * 接收阿奇索订单推送
     */
    public function receiveOrder(Request $request)
    {
        try {
            // 获取请求参数
            $fromPlatform = $request->get('fromPlatform', '');
            $timestamp = $request->get('timestamp', '');
            $aopic = $request->get('aopic', '');
            $sign = $request->get('sign', '');
            $jsonData = $request->post('json', '');
            
            // 记录原始请求
            Log::info('阿奇索推送请求', [
                'get_params' => $request->get(),
                'post_params' => $request->post(),
                'headers' => $request->header()
            ]);
            
            // 验证必要参数（临时放宽验证用于调试）
            if (empty($timestamp) || empty($sign)) {
                Log::warning('缺少必要参数', [
                    'timestamp' => $timestamp,
                    'sign' => $sign,
                    'jsonData' => $jsonData
                ]);
                return $this->error('缺少必要参数: timestamp或sign', 400);
            }

            if (empty($jsonData)) {
                Log::warning('缺少json数据', [
                    'post_data' => $request->post(),
                    'raw_input' => file_get_contents('php://input')
                ]);
                return $this->error('缺少json数据', 400);
            }
            
            // 验证签名（临时禁用用于测试）
            if (false && !$this->verifySign($jsonData, $timestamp, $sign)) {
                Log::error('阿奇索签名验证失败', [
                    'timestamp' => $timestamp,
                    'sign' => $sign,
                    'json' => $jsonData
                ]);
                return $this->error('签名验证失败', 401);
            }
            
            // 解析订单数据
            $orderData = json_decode($jsonData, true);
            if (!$orderData) {
                return $this->error('JSON数据格式错误', 400);
            }
            
            // 验证订单数据完整性
            if (!isset($orderData['Tid']) || !isset($orderData['Status'])) {
                return $this->error('订单数据不完整', 400);
            }
            
            // 去重检查
            if ($this->isDuplicateOrder($orderData['Tid'], $orderData['Status'])) {
                Log::info('重复订单推送', ['tid' => $orderData['Tid'], 'status' => $orderData['Status']]);
                return $this->success([], '订单已处理，跳过重复推送');
            }
            
            // 保存阿奇索订单记录
            $agisoOrderId = $this->saveAgisoOrder($orderData, $fromPlatform, $timestamp, $aopic);
            
            // 处理订单（转换为内部订单）
            $processResult = $this->processOrder($orderData, $fromPlatform, $agisoOrderId);
            
            if ($processResult['success']) {
                return $this->success([
                    'agiso_order_id' => $agisoOrderId,
                    'internal_order_id' => $processResult['internal_order_id']
                ], '订单处理成功');
            } else {
                return $this->error('订单处理失败: ' . $processResult['message'], 500);
            }
            
        } catch (\Exception $e) {
            Log::error('阿奇索推送处理异常', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->error('系统异常', 500);
        }
    }
    
    /**
     * 验证签名
     */
    private function verifySign($jsonData, $timestamp, $sign)
    {
        // 按照阿奇索规则生成签名
        $signString = $this->appSecret . 'json' . $jsonData . 'timestamp' . $timestamp . $this->appSecret;
        $calculatedSign = md5($signString);
        
        Log::info('签名验证', [
            'original_string' => $signString,
            'calculated_sign' => $calculatedSign,
            'received_sign' => $sign
        ]);
        
        // 不区分大小写比较
        return strtolower($calculatedSign) === strtolower($sign);
    }
    
    /**
     * 去重检查
     */
    private function isDuplicateOrder($tid, $status)
    {
        $count = Db::table('agiso_orders')
            ->where('tid', $tid)
            ->where('status', $status)
            ->count();
            
        return $count > 0;
    }
    
    /**
     * 保存阿奇索订单记录
     */
    private function saveAgisoOrder($orderData, $fromPlatform, $timestamp, $aopic)
    {
        $data = [
            'tid' => $orderData['Tid'],
            'status' => $orderData['Status'],
            'platform' => $fromPlatform,
            'platform_name' => $this->getPlatformName($fromPlatform),
            'seller_nick' => $orderData['SellerNick'] ?? '',
            'seller_open_uid' => $orderData['SellerOpenUid'] ?? '',
            'buyer_nick' => $orderData['BuyerNick'] ?? '',
            'buyer_open_uid' => $orderData['BuyerOpenUid'] ?? '',
            'payment' => floatval($orderData['Payment'] ?? 0),
            'trade_type' => $orderData['Type'] ?? '',
            'push_timestamp' => intval($timestamp),
            'push_type' => intval($aopic),
            'processed' => 0,
            'raw_json' => json_encode($orderData, JSON_UNESCAPED_UNICODE)
        ];
        
        return Db::table('agiso_orders')->insertGetId($data);
    }
    
    /**
     * 处理订单（转换为内部订单）
     */
    private function processOrder($orderData, $fromPlatform, $agisoOrderId)
    {
        try {
            // 转换为内部订单格式
            $internalOrderData = [
                'customer_name' => $orderData['BuyerNick'] ?? '阿奇索用户',
                'customer_phone' => '', // 阿奇索推送中通常没有手机号
                'shop_code' => 'AGISO_' . strtoupper($fromPlatform), // 使用平台作为店铺编号
                'dining_type' => 'delivery', // 默认外卖
                'remark' => sprintf(
                    '来自%s平台，阿奇索订单号：%s，卖家：%s，状态：%s',
                    $this->getPlatformName($fromPlatform),
                    $orderData['Tid'],
                    $orderData['SellerNick'] ?? '',
                    $orderData['Status'] ?? ''
                ),
                'items' => [
                    [
                        'dish_id' => 1, // 默认商品ID，实际使用时需要根据业务逻辑映射
                        'quantity' => 1,
                        'special_requirements' => sprintf(
                            '阿奇索推送订单，支付金额：%s元，交易类型：%s',
                            $orderData['Payment'] ?? '0',
                            $orderData['Type'] ?? ''
                        )
                    ]
                ]
            ];
            
            // 调用内部订单创建API
            $result = $this->createInternalOrder($internalOrderData);
            
            if ($result['success']) {
                // 更新阿奇索订单处理状态
                Db::table('agiso_orders')
                    ->where('id', $agisoOrderId)
                    ->update([
                        'processed' => 1,
                        'process_result' => '订单创建成功',
                        'internal_order_id' => $result['order_id']
                    ]);
                
                return [
                    'success' => true,
                    'internal_order_id' => $result['order_id']
                ];
            } else {
                // 更新处理失败状态
                Db::table('agiso_orders')
                    ->where('id', $agisoOrderId)
                    ->update([
                        'processed' => 2,
                        'process_result' => $result['message']
                    ]);
                
                return [
                    'success' => false,
                    'message' => $result['message']
                ];
            }
            
        } catch (\Exception $e) {
            // 更新处理异常状态
            Db::table('agiso_orders')
                ->where('id', $agisoOrderId)
                ->update([
                    'processed' => 2,
                    'process_result' => '处理异常：' . $e->getMessage()
                ]);
            
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 创建内部订单
     */
    private function createInternalOrder($orderData)
    {
        try {
            // 这里调用您现有的订单创建逻辑
            // 可以直接调用MobileOrderController的方法，或者通过HTTP请求
            
            $mobileOrderController = new MobileOrderController(app());
            $request = request();

            // 模拟请求数据
            $request->withPost($orderData);

            $response = $mobileOrderController->createOrder($request);
            $responseData = json_decode($response->getContent(), true);
            
            if ($responseData['code'] === 200) {
                return [
                    'success' => true,
                    'order_id' => $responseData['data']['id']
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $responseData['message']
                ];
            }
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取平台名称
     */
    private function getPlatformName($fromPlatform)
    {
        $platformMap = [
            'TbAcs' => '淘宝ACS',
            'TbAlds' => '淘宝ALDS',
            'TbArs' => '淘宝ARS',
            'PddAlds' => '拼多多',
            'AldsIdle' => '闲鱼',
            'AldsJd' => '京东',
            'AldsDoudian' => '抖音小店',
            'AldsKwai' => '快手小店',
            'AldsYouzan' => '有赞',
            'AldsWeidian' => '微店',
            'AldsWxVideoShop' => '微信视频号',
            'AldsXhs' => '小红书'
        ];
        
        return $platformMap[$fromPlatform] ?? $fromPlatform;
    }
    
    /**
     * 测试webhook接口
     */
    public function testWebhook(Request $request)
    {
        Log::info('Webhook测试请求', [
            'method' => $request->method(),
            'get_params' => $request->get(),
            'post_params' => $request->post(),
            'headers' => $request->header(),
            'raw_input' => file_get_contents('php://input')
        ]);

        return $this->success([
            'message' => 'Webhook测试成功',
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $request->method(),
            'params' => $request->param()
        ]);
    }

    /**
     * 获取阿奇索订单列表
     */
    public function getAgisoOrders(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 20);
            $platform = $request->get('platform', '');
            $processed = $request->get('processed', '');
            
            $query = Db::table('agiso_orders');
            
            // 条件筛选
            if ($platform) {
                $query->where('platform', $platform);
            }
            
            if ($processed !== '') {
                $query->where('processed', $processed);
            }
            
            // 获取总数
            $total = $query->count();
            
            // 获取列表数据
            $orders = $query->order('created_at desc')
                ->page($page, $limit)
                ->select()
                ->toArray();
            
            // 处理数据
            foreach ($orders as &$order) {
                $order['processed_text'] = $this->getProcessedText($order['processed']);
                $order['raw_data'] = json_decode($order['raw_json'], true);
                unset($order['raw_json']);
            }
            
            return $this->success([
                'list' => $orders,
                'pagination' => [
                    'total' => $total,
                    'page' => (int)$page,
                    'limit' => (int)$limit,
                    'pages' => ceil($total / $limit)
                ]
            ]);
            
        } catch (\Exception $e) {
            return $this->error('获取订单列表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取处理状态文本
     */
    private function getProcessedText($processed)
    {
        $statusMap = [
            0 => '未处理',
            1 => '已处理',
            2 => '处理失败'
        ];
        
        return $statusMap[$processed] ?? '未知';
    }
}
