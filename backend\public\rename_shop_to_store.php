<?php
// 将shop_code字段重命名为store_code

try {
    // 数据库连接配置
    $config = [
        'host' => 'localhost',
        'dbname' => 'orderflow',
        'username' => 'root',
        'password' => 'root',
        'charset' => 'utf8mb4'
    ];
    
    // 创建PDO连接
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>将shop_code字段重命名为store_code</h2>";
    echo "<p>开始修改数据库字段名...</p>";
    
    // 需要修改的表
    $tables_to_modify = ['restaurant_orders', 'orders'];
    
    foreach ($tables_to_modify as $table) {
        echo "<h3>📋 处理表: {$table}</h3>";
        
        // 检查表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if (!$stmt->fetch()) {
            echo "<p style='color: orange;'>⚠️ 表 {$table} 不存在，跳过</p>";
            continue;
        }
        
        // 检查当前表结构
        $stmt = $pdo->query("DESCRIBE {$table}");
        $columns = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $columns[] = $row['Field'];
        }
        
        $hasShopCode = in_array('shop_code', $columns);
        $hasShopName = in_array('shop_name', $columns);
        $hasStoreCode = in_array('store_code', $columns);
        $hasStoreName = in_array('store_name', $columns);
        
        echo "<p>当前字段状态:</p>";
        echo "<ul>";
        echo "<li>shop_code: " . ($hasShopCode ? '✅ 存在' : '❌ 不存在') . "</li>";
        echo "<li>shop_name: " . ($hasShopName ? '✅ 存在' : '❌ 不存在') . "</li>";
        echo "<li>store_code: " . ($hasStoreCode ? '✅ 存在' : '❌ 不存在') . "</li>";
        echo "<li>store_name: " . ($hasStoreName ? '✅ 存在' : '❌ 不存在') . "</li>";
        echo "</ul>";
        
        // 重命名shop_code为store_code
        if ($hasShopCode && !$hasStoreCode) {
            echo "<p>📝 重命名 shop_code 为 store_code...</p>";
            $pdo->exec("ALTER TABLE {$table} CHANGE shop_code store_code VARCHAR(50) DEFAULT NULL COMMENT '门店编号'");
            echo "<p style='color: green;'>✅ shop_code 重命名为 store_code 成功</p>";
        } elseif ($hasStoreCode) {
            echo "<p style='color: orange;'>ℹ️ store_code 字段已存在，跳过重命名</p>";
        } else {
            echo "<p style='color: orange;'>ℹ️ shop_code 字段不存在，跳过重命名</p>";
        }
        
        // 重命名shop_name为store_name
        if ($hasShopName && !$hasStoreName) {
            echo "<p>📝 重命名 shop_name 为 store_name...</p>";
            $pdo->exec("ALTER TABLE {$table} CHANGE shop_name store_name VARCHAR(100) DEFAULT NULL COMMENT '门店名称'");
            echo "<p style='color: green;'>✅ shop_name 重命名为 store_name 成功</p>";
        } elseif ($hasStoreName) {
            echo "<p style='color: orange;'>ℹ️ store_name 字段已存在，跳过重命名</p>";
        } else {
            echo "<p style='color: orange;'>ℹ️ shop_name 字段不存在，跳过重命名</p>";
        }
        
        // 处理索引
        echo "<p>📝 处理索引...</p>";
        try {
            // 删除旧索引
            $pdo->exec("ALTER TABLE {$table} DROP INDEX idx_shop_code");
            echo "<p style='color: green;'>✅ 删除旧索引 idx_shop_code 成功</p>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), "check that column/key exists") !== false) {
                echo "<p style='color: orange;'>ℹ️ 旧索引 idx_shop_code 不存在，跳过删除</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ 删除旧索引时出现警告: " . $e->getMessage() . "</p>";
            }
        }
        
        try {
            // 添加新索引
            $pdo->exec("ALTER TABLE {$table} ADD INDEX idx_store_code (store_code)");
            echo "<p style='color: green;'>✅ 添加新索引 idx_store_code 成功</p>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<p style='color: orange;'>ℹ️ 新索引 idx_store_code 已存在，跳过添加</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ 添加新索引时出现警告: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<hr>";
    }
    
    // 验证修改结果
    echo "<h3>📊 验证修改结果</h3>";
    
    foreach ($tables_to_modify as $table) {
        // 检查表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if (!$stmt->fetch()) {
            continue;
        }
        
        echo "<h4>{$table}表结构（修改后）:</h4>";
        $stmt = $pdo->query("DESCRIBE {$table}");
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'><th>字段名</th><th>类型</th><th>允许NULL</th><th>默认值</th><th>注释</th></tr>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $default = $row['Default'] ?? 'NULL';
            $bgColor = in_array($row['Field'], ['store_code', 'store_name']) ? 'background-color: #e6ffe6;' : '';
            echo "<tr style='{$bgColor}'>";
            echo "<td>{$row['Field']}</td>";
            echo "<td>{$row['Type']}</td>";
            echo "<td>{$row['Null']}</td>";
            echo "<td>{$default}</td>";
            echo "<td>" . ($row['Comment'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<br>";
    }
    
    echo "<h3 style='color: green;'>🎉 字段重命名完成！</h3>";
    echo "<div style='background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 修改内容总结:</h4>";
    echo "<ul>";
    echo "<li>✅ shop_code 重命名为 store_code（门店编号）</li>";
    echo "<li>✅ shop_name 重命名为 store_name（门店名称）</li>";
    echo "<li>✅ 更新了相关索引</li>";
    echo "</ul>";
    echo "<h4>🔧 接下来需要修改的代码:</h4>";
    echo "<ul>";
    echo "<li>📱 修改MobileOrderController中的字段名</li>";
    echo "<li>🔍 修改API接口参数名</li>";
    echo "<li>📄 更新相关文档</li>";
    echo "</ul>";
    echo "<h4>📝 新的API使用方式:</h4>";
    echo "<pre style='background-color: #f5f5f5; padding: 10px; border-radius: 3px;'>";
    echo "POST /api/mobile/orders\n";
    echo "{\n";
    echo "  \"store_code\": \"STORE001\",  // 改为store_code\n";
    echo "  \"customer_name\": \"张三\",\n";
    echo "  \"customer_phone\": \"13800138000\",\n";
    echo "  \"items\": [...]\n";
    echo "}";
    echo "</pre>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ 数据库操作失败</h3>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>请检查数据库连接配置和权限。</p>";
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ 执行失败</h3>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
}
?>
