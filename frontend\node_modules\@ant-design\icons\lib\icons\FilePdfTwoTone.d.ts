import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![file-pdf](data:image/svg+xml;base64,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) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
