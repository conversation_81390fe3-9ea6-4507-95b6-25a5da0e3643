import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![file-unknown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC43YzYgNiA5LjQgMTQuMSA5LjQgMjIuNlY5MjhjMCAxNy43LTE0LjMgMzItMzIgMzJIMTkyYy0xNy43IDAtMzItMTQuMy0zMi0zMlY5NmMwLTE3LjcgMTQuMy0zMiAzMi0zMmg0MjQuN2M4LjUgMCAxNi43IDMuNCAyMi43IDkuNGwyMTUuMiAyMTUuM3pNNzkwLjIgMzI2TDYwMiAxMzcuOFYzMjZoMTg4LjJ6TTQwMiA1NDljMCA1LjQgNC40IDkuNSA5LjggOS41aDMyLjRjNS40IDAgOS44LTQuMiA5LjgtOS40IDAtMjguMiAyNS44LTUxLjYgNTgtNTEuNnM1OCAyMy40IDU4IDUxLjVjMCAyNS4zLTIxIDQ3LjItNDkuMyA1MC45LTE5LjMgMi44LTM0LjUgMjAuMy0zNC43IDQwLjF2MzJjMCA1LjUgNC41IDEwIDEwIDEwaDMyYzUuNSAwIDEwLTQuNSAxMC0xMHYtMTIuMmMwLTYgNC0xMS41IDkuNy0xMy4zIDQ0LjYtMTQuNCA3NS01NCA3NC4zLTk4LjktLjgtNTUuNS00OS4yLTEwMC44LTEwOC41LTEwMS42LTYxLjQtLjctMTExLjUgNDUuNi0xMTEuNSAxMDN6bTExMCAyMjdhMzIgMzIgMCAxMDAtNjQgMzIgMzIgMCAwMDAgNjR6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
