<?php
// 为restaurant_orders表添加店铺相关字段

try {
    // 数据库连接配置
    $config = [
        'host' => 'localhost',
        'dbname' => 'orderflow',
        'username' => 'root',
        'password' => 'root',
        'charset' => 'utf8mb4'
    ];
    
    // 创建PDO连接
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>为restaurant_orders表添加店铺字段</h2>";
    echo "<p>开始修改数据库结构...</p>";
    
    // 检查restaurant_orders表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'restaurant_orders'");
    if (!$stmt->fetch()) {
        echo "<p style='color: red;'>❌ restaurant_orders表不存在</p>";
        exit;
    }
    
    // 检查当前restaurant_orders表结构
    echo "<h3>📋 检查restaurant_orders表当前结构</h3>";
    $stmt = $pdo->query("DESCRIBE restaurant_orders");
    $columns = [];
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'><th>字段名</th><th>类型</th><th>允许NULL</th><th>默认值</th><th>注释</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $columns[] = $row['Field'];
        $default = $row['Default'] ?? 'NULL';
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$default}</td>";
        echo "<td>" . ($row['Comment'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 检查是否已经有店铺相关字段
    $hasShopCode = in_array('shop_code', $columns);
    $hasShopName = in_array('shop_name', $columns);
    
    echo "<h3>🔧 开始添加店铺字段</h3>";
    
    if ($hasShopCode && $hasShopName) {
        echo "<p style='color: orange;'>ℹ️ 店铺字段已存在，跳过添加</p>";
    } else {
        // 添加店铺编号字段
        if (!$hasShopCode) {
            echo "<p>📝 添加店铺编号字段 (shop_code)...</p>";
            $pdo->exec("
                ALTER TABLE restaurant_orders 
                ADD COLUMN shop_code VARCHAR(50) DEFAULT NULL COMMENT '店铺编号' 
                AFTER customer_phone
            ");
            echo "<p style='color: green;'>✅ shop_code字段添加成功</p>";
        } else {
            echo "<p style='color: orange;'>ℹ️ shop_code字段已存在，跳过</p>";
        }
        
        // 添加店铺名称字段
        if (!$hasShopName) {
            echo "<p>📝 添加店铺名称字段 (shop_name)...</p>";
            $pdo->exec("
                ALTER TABLE restaurant_orders 
                ADD COLUMN shop_name VARCHAR(100) DEFAULT NULL COMMENT '店铺名称' 
                AFTER shop_code
            ");
            echo "<p style='color: green;'>✅ shop_name字段添加成功</p>";
        } else {
            echo "<p style='color: orange;'>ℹ️ shop_name字段已存在，跳过</p>";
        }
        
        // 添加索引
        echo "<p>📝 添加索引...</p>";
        try {
            $pdo->exec("ALTER TABLE restaurant_orders ADD INDEX idx_shop_code (shop_code)");
            echo "<p style='color: green;'>✅ shop_code索引添加成功</p>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<p style='color: orange;'>ℹ️ shop_code索引已存在，跳过</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ 添加索引时出现警告: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // 验证修改结果
    echo "<h3>📊 验证修改结果</h3>";
    
    // 显示修改后的restaurant_orders表结构
    echo "<h4>restaurant_orders表结构（修改后）:</h4>";
    $stmt = $pdo->query("DESCRIBE restaurant_orders");
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'><th>字段名</th><th>类型</th><th>允许NULL</th><th>默认值</th><th>注释</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $default = $row['Default'] ?? 'NULL';
        $bgColor = in_array($row['Field'], ['shop_code', 'shop_name']) ? 'background-color: #e6ffe6;' : '';
        echo "<tr style='{$bgColor}'>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$default}</td>";
        echo "<td>" . ($row['Comment'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3 style='color: green;'>🎉 restaurant_orders表修改完成！</h3>";
    echo "<div style='background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 修改内容总结:</h4>";
    echo "<ul>";
    echo "<li>✅ 在restaurant_orders表中添加了shop_code字段（店铺编号）</li>";
    echo "<li>✅ 在restaurant_orders表中添加了shop_name字段（店铺名称）</li>";
    echo "<li>✅ 为shop_code字段添加了索引</li>";
    echo "</ul>";
    echo "<h4>🔧 API使用说明:</h4>";
    echo "<ul>";
    echo "<li>📱 在调用 /api/mobile/orders 接口时，可以传递 shop_code 参数</li>";
    echo "<li>🔍 系统会自动根据shop_code查询store_info表获取shop_name</li>";
    echo "<li>💾 shop_code和shop_name会自动保存到订单中</li>";
    echo "</ul>";
    echo "<h4>📝 请求示例:</h4>";
    echo "<pre style='background-color: #f5f5f5; padding: 10px; border-radius: 3px;'>";
    echo "POST /api/mobile/orders\n";
    echo "{\n";
    echo "  \"shop_code\": \"SHOP001\",\n";
    echo "  \"customer_name\": \"张三\",\n";
    echo "  \"customer_phone\": \"13800138000\",\n";
    echo "  \"items\": [...]\n";
    echo "}";
    echo "</pre>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ 数据库操作失败</h3>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>请检查数据库连接配置和权限。</p>";
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ 执行失败</h3>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
}
?>
