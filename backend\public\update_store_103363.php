<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=orderflow;charset=utf8mb4', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $pdo->exec("UPDATE store_info SET store_id = '103363' WHERE id = (SELECT id FROM (SELECT id FROM store_info ORDER BY id DESC LIMIT 1) t)");
    echo "更新门店103363的store_id成功";
} catch (Exception $e) {
    echo "错误: " . $e->getMessage();
}
?>
