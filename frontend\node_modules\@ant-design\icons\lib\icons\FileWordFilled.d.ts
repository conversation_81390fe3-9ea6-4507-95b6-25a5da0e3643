import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![file-word](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC43YzYgNiA5LjQgMTQuMSA5LjQgMjIuNlY5MjhjMCAxNy43LTE0LjMgMzItMzIgMzJIMTkyYy0xNy43IDAtMzItMTQuMy0zMi0zMlY5NmMwLTE3LjcgMTQuMy0zMiAzMi0zMmg0MjQuN2M4LjUgMCAxNi43IDMuNCAyMi43IDkuNGwyMTUuMiAyMTUuM3pNNzkwLjIgMzI2TDYwMiAxMzcuOFYzMjZoMTg4LjJ6TTUxMiA1NjYuMWw1Mi44MSAxOTdhMTIgMTIgMCAwMDExLjYgOC45aDMxLjc3YTEyIDEyIDAgMDAxMS42LTguODhsNzQuMzctMjc2YTEyIDEyIDAgMDAuNC0zLjEyIDEyIDEyIDAgMDAtMTItMTJoLTM1LjU3YTEyIDEyIDAgMDAtMTEuNyA5LjMxbC00NS43OCAxOTkuMS00OS43Ni0xOTkuMzJBMTIgMTIgMCAwMDUyOC4xIDQ3MmgtMzIuMmExMiAxMiAwIDAwLTExLjY0IDkuMUw0MzQuNiA2ODAuMDEgMzg4LjUgNDgxLjNhMTIgMTIgMCAwMC0xMS42OC05LjI5aC0zNS4zOWExMiAxMiAwIDAwLTMuMTEuNDEgMTIgMTIgMCAwMC04LjQ3IDE0LjdsNzQuMTcgMjc2QTEyIDEyIDAgMDA0MTUuNiA3NzJoMzEuOTlhMTIgMTIgMCAwMDExLjU5LTguOWw1Mi44MS0xOTd6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
