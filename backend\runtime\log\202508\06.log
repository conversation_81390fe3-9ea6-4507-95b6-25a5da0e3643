[2025-08-06T05:13:58+08:00][info] Loading API routes
[2025-08-06T05:13:58+08:00][sql] CONNECT:[ UseTime:0.009413s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:13:58+08:00][sql] SHOW FULL COLUMNS FROM `shops` [ RunTime:0.012760s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`shop_name` FROM `shops` WHERE  `status` = 1 ORDER BY `created_at` DESC [ RunTime:0.001799s ]
[2025-08-06T05:13:58+08:00][info] Loading API routes
[2025-08-06T05:13:58+08:00][sql] CONNECT:[ UseTime:0.001810s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:13:58+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.005161s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`name`,`price` FROM `dishes` WHERE  `status` = 1 ORDER BY `created_at` DESC [ RunTime:0.001313s ]
[2025-08-06T05:13:58+08:00][info] Loading API routes
[2025-08-06T05:13:58+08:00][sql] CONNECT:[ UseTime:0.002137s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:13:58+08:00][sql] SHOW FULL COLUMNS FROM `combos` [ RunTime:0.004065s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`name`,`price` FROM `combos` WHERE  `status` = 1 ORDER BY `created_at` DESC [ RunTime:0.000995s ]
[2025-08-06T05:13:58+08:00][info] Loading API routes
[2025-08-06T05:13:58+08:00][sql] CONNECT:[ UseTime:0.001633s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:13:58+08:00][sql] SHOW FULL COLUMNS FROM `shop_products` [ RunTime:0.004465s ]
[2025-08-06T05:13:58+08:00][sql] SELECT COUNT(*) AS think_count FROM `shop_products` `sp` LEFT JOIN `shops` `s` ON `sp`.`shop_id`=`s`.`id` [ RunTime:0.001239s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `sp`.*,`s`.`shop_name`,s.shop_id as shop_code FROM `shop_products` `sp` LEFT JOIN `shops` `s` ON `sp`.`shop_id`=`s`.`id` ORDER BY `sp`.`shop_id` ASC,`sp`.`sort_order` ASC,`sp`.`created_at` DESC LIMIT 0,20 [ RunTime:0.001293s ]
[2025-08-06T05:13:58+08:00][sql] SHOW FULL COLUMNS FROM `combos` [ RunTime:0.003008s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `combos` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000626s ]
[2025-08-06T05:13:58+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.003460s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000823s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 2 LIMIT 1 [ RunTime:0.000726s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 3 LIMIT 1 [ RunTime:0.000553s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `combos` WHERE  `id` = 2 LIMIT 1 [ RunTime:0.000702s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 4 LIMIT 1 [ RunTime:0.000689s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 5 LIMIT 1 [ RunTime:0.000617s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `combos` WHERE  `id` = 3 LIMIT 1 [ RunTime:0.000628s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 6 LIMIT 1 [ RunTime:0.000584s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 7 LIMIT 1 [ RunTime:0.000556s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 8 LIMIT 1 [ RunTime:0.000508s ]
[2025-08-06T05:13:58+08:00][info] Loading API routes
[2025-08-06T05:13:58+08:00][sql] CONNECT:[ UseTime:0.001271s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:13:58+08:00][sql] SHOW FULL COLUMNS FROM `shops` [ RunTime:0.004297s ]
[2025-08-06T05:13:58+08:00][sql] SELECT `id`,`shop_name` FROM `shops` WHERE  `status` = 1 ORDER BY `created_at` DESC [ RunTime:0.001144s ]
[2025-08-06T05:13:58+08:00][info] Loading API routes
[2025-08-06T05:13:59+08:00][sql] CONNECT:[ UseTime:0.001216s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:13:59+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.004542s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price` FROM `dishes` WHERE  `status` = 1 ORDER BY `created_at` DESC [ RunTime:0.000833s ]
[2025-08-06T05:13:59+08:00][info] Loading API routes
[2025-08-06T05:13:59+08:00][sql] CONNECT:[ UseTime:0.001078s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:13:59+08:00][sql] SHOW FULL COLUMNS FROM `combos` [ RunTime:0.003446s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price` FROM `combos` WHERE  `status` = 1 ORDER BY `created_at` DESC [ RunTime:0.000809s ]
[2025-08-06T05:13:59+08:00][info] Loading API routes
[2025-08-06T05:13:59+08:00][sql] CONNECT:[ UseTime:0.001159s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:13:59+08:00][sql] SHOW FULL COLUMNS FROM `shop_products` [ RunTime:0.003318s ]
[2025-08-06T05:13:59+08:00][sql] SELECT COUNT(*) AS think_count FROM `shop_products` `sp` LEFT JOIN `shops` `s` ON `sp`.`shop_id`=`s`.`id` [ RunTime:0.000789s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `sp`.*,`s`.`shop_name`,s.shop_id as shop_code FROM `shop_products` `sp` LEFT JOIN `shops` `s` ON `sp`.`shop_id`=`s`.`id` ORDER BY `sp`.`shop_id` ASC,`sp`.`sort_order` ASC,`sp`.`created_at` DESC LIMIT 0,20 [ RunTime:0.000673s ]
[2025-08-06T05:13:59+08:00][sql] SHOW FULL COLUMNS FROM `combos` [ RunTime:0.002960s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `combos` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000772s ]
[2025-08-06T05:13:59+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.002899s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000622s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 2 LIMIT 1 [ RunTime:0.000388s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 3 LIMIT 1 [ RunTime:0.000371s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `combos` WHERE  `id` = 2 LIMIT 1 [ RunTime:0.000387s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 4 LIMIT 1 [ RunTime:0.000382s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 5 LIMIT 1 [ RunTime:0.000382s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `combos` WHERE  `id` = 3 LIMIT 1 [ RunTime:0.000373s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 6 LIMIT 1 [ RunTime:0.000386s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 7 LIMIT 1 [ RunTime:0.000374s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 8 LIMIT 1 [ RunTime:0.000356s ]
[2025-08-06T05:13:59+08:00][info] Loading API routes
[2025-08-06T05:13:59+08:00][sql] CONNECT:[ UseTime:0.002220s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:13:59+08:00][sql] SHOW FULL COLUMNS FROM `shop_products` [ RunTime:0.006596s ]
[2025-08-06T05:13:59+08:00][sql] SELECT COUNT(*) AS think_count FROM `shop_products` `sp` LEFT JOIN `shops` `s` ON `sp`.`shop_id`=`s`.`id` [ RunTime:0.001935s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `sp`.*,`s`.`shop_name`,s.shop_id as shop_code FROM `shop_products` `sp` LEFT JOIN `shops` `s` ON `sp`.`shop_id`=`s`.`id` ORDER BY `sp`.`shop_id` ASC,`sp`.`sort_order` ASC,`sp`.`created_at` DESC LIMIT 0,20 [ RunTime:0.000826s ]
[2025-08-06T05:13:59+08:00][sql] SHOW FULL COLUMNS FROM `combos` [ RunTime:0.004603s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `combos` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.001251s ]
[2025-08-06T05:13:59+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.005178s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000996s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 2 LIMIT 1 [ RunTime:0.000915s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 3 LIMIT 1 [ RunTime:0.000893s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `combos` WHERE  `id` = 2 LIMIT 1 [ RunTime:0.000703s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 4 LIMIT 1 [ RunTime:0.000761s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 5 LIMIT 1 [ RunTime:0.000716s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `combos` WHERE  `id` = 3 LIMIT 1 [ RunTime:0.000769s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 6 LIMIT 1 [ RunTime:0.000833s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 7 LIMIT 1 [ RunTime:0.000711s ]
[2025-08-06T05:13:59+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 8 LIMIT 1 [ RunTime:0.000769s ]
[2025-08-06T05:14:00+08:00][info] Loading API routes
[2025-08-06T05:14:00+08:00][sql] CONNECT:[ UseTime:0.001321s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:14:00+08:00][sql] SHOW FULL COLUMNS FROM `shop_products` [ RunTime:0.003638s ]
[2025-08-06T05:14:00+08:00][sql] SELECT COUNT(*) AS think_count FROM `shop_products` `sp` LEFT JOIN `shops` `s` ON `sp`.`shop_id`=`s`.`id` [ RunTime:0.001082s ]
[2025-08-06T05:14:00+08:00][sql] SELECT `sp`.*,`s`.`shop_name`,s.shop_id as shop_code FROM `shop_products` `sp` LEFT JOIN `shops` `s` ON `sp`.`shop_id`=`s`.`id` ORDER BY `sp`.`shop_id` ASC,`sp`.`sort_order` ASC,`sp`.`created_at` DESC LIMIT 0,20 [ RunTime:0.000722s ]
[2025-08-06T05:14:00+08:00][sql] SHOW FULL COLUMNS FROM `combos` [ RunTime:0.002560s ]
[2025-08-06T05:14:00+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `combos` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000833s ]
[2025-08-06T05:14:00+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.002989s ]
[2025-08-06T05:14:00+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000640s ]
[2025-08-06T05:14:00+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 2 LIMIT 1 [ RunTime:0.000460s ]
[2025-08-06T05:14:00+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 3 LIMIT 1 [ RunTime:0.000476s ]
[2025-08-06T05:14:00+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `combos` WHERE  `id` = 2 LIMIT 1 [ RunTime:0.000433s ]
[2025-08-06T05:14:00+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 4 LIMIT 1 [ RunTime:0.000417s ]
[2025-08-06T05:14:00+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 5 LIMIT 1 [ RunTime:0.000428s ]
[2025-08-06T05:14:00+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `combos` WHERE  `id` = 3 LIMIT 1 [ RunTime:0.000460s ]
[2025-08-06T05:14:00+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 6 LIMIT 1 [ RunTime:0.000433s ]
[2025-08-06T05:14:00+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 7 LIMIT 1 [ RunTime:0.000414s ]
[2025-08-06T05:14:00+08:00][sql] SELECT `id`,`name`,`price`,`description` FROM `dishes` WHERE  `id` = 8 LIMIT 1 [ RunTime:0.000407s ]
[2025-08-06T05:14:07+08:00][info] Loading API routes
[2025-08-06T05:14:07+08:00][sql] CONNECT:[ UseTime:0.001173s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:14:07+08:00][sql] SHOW FULL COLUMNS FROM `dish_categories` [ RunTime:0.003495s ]
[2025-08-06T05:14:07+08:00][sql] SELECT * FROM `dish_categories` ORDER BY `sort_order` ASC,`id` ASC LIMIT 0,20 [ RunTime:0.002628s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dish_categories` [ RunTime:0.000620s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dish_categories` WHERE  `parent_id` = 1 [ RunTime:0.000780s ]
[2025-08-06T05:14:07+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.004726s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` WHERE  `category_id` = 1 [ RunTime:0.001026s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dish_categories` WHERE  `parent_id` = 2 [ RunTime:0.000944s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` WHERE  `category_id` = 2 [ RunTime:0.000835s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dish_categories` WHERE  `parent_id` = 3 [ RunTime:0.000482s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` WHERE  `category_id` = 3 [ RunTime:0.000973s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dish_categories` WHERE  `parent_id` = 4 [ RunTime:0.000788s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` WHERE  `category_id` = 4 [ RunTime:0.000813s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dish_categories` WHERE  `parent_id` = 5 [ RunTime:0.000628s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` WHERE  `category_id` = 5 [ RunTime:0.000684s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dish_categories` WHERE  `parent_id` = 11 [ RunTime:0.000608s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` WHERE  `category_id` = 11 [ RunTime:0.000643s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dish_categories` WHERE  `parent_id` = 6 [ RunTime:0.000578s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` WHERE  `category_id` = 6 [ RunTime:0.000525s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dish_categories` WHERE  `parent_id` = 7 [ RunTime:0.000542s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` WHERE  `category_id` = 7 [ RunTime:0.000841s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dish_categories` WHERE  `parent_id` = 8 [ RunTime:0.000708s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` WHERE  `category_id` = 8 [ RunTime:0.000725s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dish_categories` WHERE  `parent_id` = 9 [ RunTime:0.000573s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` WHERE  `category_id` = 9 [ RunTime:0.000518s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dish_categories` WHERE  `parent_id` = 10 [ RunTime:0.000499s ]
[2025-08-06T05:14:07+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` WHERE  `category_id` = 10 [ RunTime:0.000437s ]
[2025-08-06T05:14:08+08:00][info] Loading API routes
[2025-08-06T05:14:08+08:00][sql] CONNECT:[ UseTime:0.001334s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:14:08+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.004244s ]
[2025-08-06T05:14:08+08:00][sql] SELECT * FROM `dishes` ORDER BY `sort_order` ASC,`id` DESC LIMIT 0,20 [ RunTime:0.000833s ]
[2025-08-06T05:14:08+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` [ RunTime:0.000739s ]
[2025-08-06T05:14:08+08:00][sql] SHOW FULL COLUMNS FROM `dish_categories` [ RunTime:0.003790s ]
[2025-08-06T05:14:08+08:00][sql] SELECT * FROM `dish_categories` WHERE  `id` = 5 LIMIT 1 [ RunTime:0.000970s ]
[2025-08-06T05:14:10+08:00][info] Loading API routes
[2025-08-06T05:14:10+08:00][sql] CONNECT:[ UseTime:0.001183s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:14:10+08:00][sql] SHOW FULL COLUMNS FROM `agiso_orders` [ RunTime:0.003865s ]
[2025-08-06T05:14:10+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59' [ RunTime:0.000943s ]
[2025-08-06T05:14:10+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0 [ RunTime:0.000715s ]
[2025-08-06T05:14:10+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0  AND `processed` = 1 [ RunTime:0.000594s ]
[2025-08-06T05:14:10+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0  AND `processed` = 1  AND `processed` = 2 [ RunTime:0.000497s ]
[2025-08-06T05:14:10+08:00][sql] SELECT SUM(`payment`) AS think_sum FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0  AND `processed` = 1  AND `processed` = 2 [ RunTime:0.000469s ]
[2025-08-06T05:14:10+08:00][sql] SELECT platform, platform_name, COUNT(*) as count, SUM(payment) as amount FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59' GROUP BY `platform`,`platform_name` [ RunTime:0.000770s ]
[2025-08-06T05:14:10+08:00][sql] SELECT DATE(created_at) as date, COUNT(*) as count, SUM(payment) as amount FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59' GROUP BY DATE(created_at) ORDER BY `date` ASC [ RunTime:0.000691s ]
[2025-08-06T05:14:10+08:00][info] Loading API routes
[2025-08-06T05:14:10+08:00][sql] CONNECT:[ UseTime:0.001230s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:14:10+08:00][sql] SHOW FULL COLUMNS FROM `agiso_orders` [ RunTime:0.003733s ]
[2025-08-06T05:14:10+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` [ RunTime:0.000551s ]
[2025-08-06T05:14:10+08:00][sql] SELECT * FROM `agiso_orders` ORDER BY `created_at` DESC LIMIT 0,20 [ RunTime:0.001122s ]
[2025-08-06T05:14:10+08:00][info] Loading API routes
[2025-08-06T05:14:10+08:00][sql] CONNECT:[ UseTime:0.010780s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:14:10+08:00][sql] SHOW FULL COLUMNS FROM `agiso_orders` [ RunTime:0.003773s ]
[2025-08-06T05:14:10+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59' [ RunTime:0.001082s ]
[2025-08-06T05:14:10+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0 [ RunTime:0.000550s ]
[2025-08-06T05:14:10+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0  AND `processed` = 1 [ RunTime:0.000671s ]
[2025-08-06T05:14:10+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0  AND `processed` = 1  AND `processed` = 2 [ RunTime:0.000710s ]
[2025-08-06T05:14:10+08:00][sql] SELECT SUM(`payment`) AS think_sum FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0  AND `processed` = 1  AND `processed` = 2 [ RunTime:0.000435s ]
[2025-08-06T05:14:10+08:00][sql] SELECT platform, platform_name, COUNT(*) as count, SUM(payment) as amount FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59' GROUP BY `platform`,`platform_name` [ RunTime:0.001155s ]
[2025-08-06T05:14:10+08:00][sql] SELECT DATE(created_at) as date, COUNT(*) as count, SUM(payment) as amount FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59' GROUP BY DATE(created_at) ORDER BY `date` ASC [ RunTime:0.000965s ]
[2025-08-06T05:14:10+08:00][info] Loading API routes
[2025-08-06T05:14:10+08:00][sql] CONNECT:[ UseTime:0.001695s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:14:10+08:00][sql] SHOW FULL COLUMNS FROM `agiso_orders` [ RunTime:0.004893s ]
[2025-08-06T05:14:10+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` [ RunTime:0.000533s ]
[2025-08-06T05:14:10+08:00][sql] SELECT * FROM `agiso_orders` ORDER BY `created_at` DESC LIMIT 0,20 [ RunTime:0.000643s ]
[2025-08-06T05:14:11+08:00][info] Loading API routes
[2025-08-06T05:14:11+08:00][sql] CONNECT:[ UseTime:0.001215s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:14:11+08:00][sql] SHOW FULL COLUMNS FROM `agiso_orders` [ RunTime:0.003238s ]
[2025-08-06T05:14:11+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` [ RunTime:0.000487s ]
[2025-08-06T05:14:11+08:00][sql] SELECT * FROM `agiso_orders` ORDER BY `created_at` DESC LIMIT 0,20 [ RunTime:0.000543s ]
[2025-08-06T05:14:11+08:00][info] Loading API routes
[2025-08-06T05:14:11+08:00][sql] CONNECT:[ UseTime:0.001360s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:14:11+08:00][sql] SHOW FULL COLUMNS FROM `agiso_orders` [ RunTime:0.003825s ]
[2025-08-06T05:14:11+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` [ RunTime:0.000624s ]
[2025-08-06T05:14:11+08:00][sql] SELECT * FROM `agiso_orders` ORDER BY `created_at` DESC LIMIT 0,20 [ RunTime:0.000649s ]
[2025-08-06T05:14:16+08:00][info] Loading API routes
[2025-08-06T05:14:21+08:00][info] Loading API routes
[2025-08-06T05:17:08+08:00][info] Loading API routes
[2025-08-06T05:17:08+08:00][sql] CONNECT:[ UseTime:0.010634s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:17:08+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.003216s ]
[2025-08-06T05:17:08+08:00][sql] SELECT `s`.*,`c`.`city_name`,ROUND(6371 * acos(cos(radians(26.0745)) * cos(radians(s.latitude)) * cos(radians(s.longitude) - radians(119.2965)) + sin(radians(26.0745)) * sin(radians(s.latitude))), 2) as distance FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` ORDER BY `distance` ASC LIMIT 1 [ RunTime:0.274267s ]
[2025-08-06T05:17:08+08:00][sql] SELECT `s`.`id`,`s`.`store_code`,`s`.`name`,`s`.`address`,`s`.`status`,`s`.`start_time`,`s`.`end_time`,`s`.`phone`,`s`.`longitude`,`s`.`latitude`,`s`.`city_code`,`c`.`city_name`,ROUND(6371 * acos(cos(radians(26.0745)) * cos(radians(s.latitude)) * cos(radians(s.longitude) - radians(119.2965)) + sin(radians(26.0745)) * sin(radians(s.latitude))), 2) as distance FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`city_code` = '350100' ORDER BY `distance` ASC [ RunTime:0.009531s ]
[2025-08-06T05:17:17+08:00][info] Loading API routes
[2025-08-06T05:17:17+08:00][sql] CONNECT:[ UseTime:0.001212s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:17:17+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.004388s ]
[2025-08-06T05:17:17+08:00][sql] SELECT `s`.*,`c`.`city_name`,ROUND(6371 * acos(cos(radians(26.0745)) * cos(radians(s.latitude)) * cos(radians(s.longitude) - radians(119.2965)) + sin(radians(26.0745)) * sin(radians(s.latitude))), 2) as distance FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` ORDER BY `distance` ASC LIMIT 1 [ RunTime:0.294731s ]
[2025-08-06T05:17:17+08:00][sql] SELECT `s`.`id`,`s`.`store_code`,`s`.`name`,`s`.`address`,`s`.`status`,`s`.`start_time`,`s`.`end_time`,`s`.`phone`,`s`.`longitude`,`s`.`latitude`,`s`.`city_code`,`c`.`city_name`,ROUND(6371 * acos(cos(radians(26.0745)) * cos(radians(s.latitude)) * cos(radians(s.longitude) - radians(119.2965)) + sin(radians(26.0745)) * sin(radians(s.latitude))), 2) as distance FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`city_code` = '350100' ORDER BY `distance` ASC [ RunTime:0.007593s ]
[2025-08-06T05:18:32+08:00][info] Loading API routes
[2025-08-06T05:18:32+08:00][sql] CONNECT:[ UseTime:0.001337s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:18:32+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.003329s ]
[2025-08-06T05:18:32+08:00][sql] SELECT * FROM `city_map` ORDER BY `first_char` ASC,`city_name` ASC [ RunTime:0.001627s ]
[2025-08-06T05:18:32+08:00][info] Loading API routes
[2025-08-06T05:18:32+08:00][sql] CONNECT:[ UseTime:0.001181s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:18:32+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002649s ]
[2025-08-06T05:18:32+08:00][sql] SELECT * FROM `city_map` WHERE  `city_code` = '110100' [ RunTime:0.000956s ]
[2025-08-06T05:18:32+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002464s ]
[2025-08-06T05:18:32+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.003332s ]
[2025-08-06T05:18:32+08:00][info] Loading API routes
[2025-08-06T05:18:32+08:00][sql] CONNECT:[ UseTime:0.001093s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:18:32+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002721s ]
[2025-08-06T05:18:32+08:00][sql] SELECT `city_code`,`city_name` FROM `city_map` WHERE  `city_code` = '110100' LIMIT 1 [ RunTime:0.000821s ]
[2025-08-06T05:18:32+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002103s ]
[2025-08-06T05:18:32+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.003428s ]
[2025-08-06T05:18:34+08:00][info] Loading API routes
[2025-08-06T05:18:34+08:00][sql] CONNECT:[ UseTime:0.001370s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:18:34+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.004464s ]
[2025-08-06T05:18:34+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.002032s ]
[2025-08-06T05:18:34+08:00][info] Loading API routes
[2025-08-06T05:18:34+08:00][sql] CONNECT:[ UseTime:0.000924s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:18:34+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002579s ]
[2025-08-06T05:18:34+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.001155s ]
[2025-08-06T05:18:34+08:00][info] Loading API routes
[2025-08-06T05:18:34+08:00][sql] CONNECT:[ UseTime:0.001107s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:18:34+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.003173s ]
[2025-08-06T05:18:34+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.001043s ]
[2025-08-06T05:18:34+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000779s ]
[2025-08-06T05:18:42+08:00][info] Loading API routes
[2025-08-06T05:18:42+08:00][sql] CONNECT:[ UseTime:0.001209s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:18:42+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.004293s ]
[2025-08-06T05:18:42+08:00][sql] SELECT * FROM `dishes` WHERE  `id` = 1  AND `status` = 1  AND `is_available` = 1 LIMIT 1 [ RunTime:0.000780s ]
[2025-08-06T05:18:42+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.005965s ]
[2025-08-06T05:18:42+08:00][sql] INSERT INTO `restaurant_orders` SET `order_no` = 'M202508060518426592' , `customer_name` = '顾客' , `customer_phone` = '13800000000' , `table_no` = '' , `order_type` = 'dine_in' , `subtotal` = '26' , `discount_amount` = '0' , `service_fee` = '0' , `delivery_fee` = '0' , `total_amount` = '26' , `status` = 'pending' , `payment_status` = 'paid' , `payment_method` = 'cash' , `special_requests` = '' [ RunTime:0.003094s ]
[2025-08-06T05:18:42+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.002927s ]
[2025-08-06T05:18:42+08:00][sql] INSERT INTO `restaurant_order_items` SET `order_id` = 10 , `dish_id` = 1 , `dish_name` = 'sqt' , `dish_sku` = '' , `unit_price` = '26.00' , `quantity` = 1 , `total_price` = '26' , `special_requests` = '' , `status` = 'pending' [ RunTime:0.001058s ]
[2025-08-06T05:18:42+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 10 LIMIT 1 [ RunTime:0.000883s ]
[2025-08-06T05:18:42+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '10' ORDER BY `oi`.`id` ASC [ RunTime:0.000741s ]
[2025-08-06T05:19:12+08:00][info] Loading API routes
[2025-08-06T05:19:12+08:00][sql] CONNECT:[ UseTime:0.010803s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:19:12+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.003627s ]
[2025-08-06T05:19:12+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 10 LIMIT 1 [ RunTime:0.001109s ]
[2025-08-06T05:19:12+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.002631s ]
[2025-08-06T05:19:12+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '10' ORDER BY `oi`.`id` ASC [ RunTime:0.001181s ]
[2025-08-06T05:19:12+08:00][info] Loading API routes
[2025-08-06T05:19:12+08:00][sql] CONNECT:[ UseTime:0.001211s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:19:12+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.003724s ]
[2025-08-06T05:19:12+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 10 LIMIT 1 [ RunTime:0.001211s ]
[2025-08-06T05:19:12+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.002788s ]
[2025-08-06T05:19:12+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '10' ORDER BY `oi`.`id` ASC [ RunTime:0.001404s ]
[2025-08-06T05:19:34+08:00][info] Loading API routes
[2025-08-06T05:19:34+08:00][sql] CONNECT:[ UseTime:0.002018s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:19:34+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.003912s ]
[2025-08-06T05:19:34+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = 'undefined' LIMIT 1 [ RunTime:0.002305s ]
[2025-08-06T05:19:35+08:00][info] Loading API routes
[2025-08-06T05:19:35+08:00][sql] CONNECT:[ UseTime:0.001268s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:19:35+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.004965s ]
[2025-08-06T05:19:35+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.001367s ]
[2025-08-06T05:19:35+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000549s ]
[2025-08-06T05:19:41+08:00][info] Loading API routes
[2025-08-06T05:19:41+08:00][sql] CONNECT:[ UseTime:0.001173s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:19:41+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.003210s ]
[2025-08-06T05:19:41+08:00][sql] SELECT * FROM `city_map` ORDER BY `first_char` ASC,`city_name` ASC [ RunTime:0.001755s ]
[2025-08-06T05:19:41+08:00][info] Loading API routes
[2025-08-06T05:19:41+08:00][sql] CONNECT:[ UseTime:0.001120s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:19:41+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002777s ]
[2025-08-06T05:19:41+08:00][sql] SELECT * FROM `city_map` WHERE  `city_code` = '110100' [ RunTime:0.001854s ]
[2025-08-06T05:19:41+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002871s ]
[2025-08-06T05:19:41+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.003748s ]
[2025-08-06T05:19:41+08:00][info] Loading API routes
[2025-08-06T05:19:41+08:00][sql] CONNECT:[ UseTime:0.001286s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:19:41+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.003044s ]
[2025-08-06T05:19:41+08:00][sql] SELECT `city_code`,`city_name` FROM `city_map` WHERE  `city_code` = '110100' LIMIT 1 [ RunTime:0.000937s ]
[2025-08-06T05:19:41+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002336s ]
[2025-08-06T05:19:41+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.003727s ]
[2025-08-06T05:19:42+08:00][info] Loading API routes
[2025-08-06T05:19:42+08:00][sql] CONNECT:[ UseTime:0.001179s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:19:42+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002967s ]
[2025-08-06T05:19:42+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.000746s ]
[2025-08-06T05:19:43+08:00][info] Loading API routes
[2025-08-06T05:19:43+08:00][sql] CONNECT:[ UseTime:0.001153s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:19:43+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002582s ]
[2025-08-06T05:19:43+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.000676s ]
[2025-08-06T05:19:43+08:00][info] Loading API routes
[2025-08-06T05:19:43+08:00][sql] CONNECT:[ UseTime:0.001117s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:19:43+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.003085s ]
[2025-08-06T05:19:43+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.001138s ]
[2025-08-06T05:19:43+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000630s ]
[2025-08-06T05:19:49+08:00][info] Loading API routes
[2025-08-06T05:19:49+08:00][sql] CONNECT:[ UseTime:0.001185s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:19:49+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.003638s ]
[2025-08-06T05:19:49+08:00][sql] SELECT * FROM `dishes` WHERE  `id` = 1  AND `status` = 1  AND `is_available` = 1 LIMIT 1 [ RunTime:0.000679s ]
[2025-08-06T05:19:49+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.003026s ]
[2025-08-06T05:19:49+08:00][sql] INSERT INTO `restaurant_orders` SET `order_no` = 'M202508060519493775' , `customer_name` = '顾客' , `customer_phone` = '13800000000' , `table_no` = '' , `order_type` = 'dine_in' , `subtotal` = '26' , `discount_amount` = '0' , `service_fee` = '0' , `delivery_fee` = '0' , `total_amount` = '26' , `status` = 'pending' , `payment_status` = 'paid' , `payment_method` = 'cash' , `special_requests` = '' [ RunTime:0.001896s ]
[2025-08-06T05:19:49+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.002735s ]
[2025-08-06T05:19:49+08:00][sql] INSERT INTO `restaurant_order_items` SET `order_id` = 11 , `dish_id` = 1 , `dish_name` = 'sqt' , `dish_sku` = '' , `unit_price` = '26.00' , `quantity` = 1 , `total_price` = '26' , `special_requests` = '' , `status` = 'pending' [ RunTime:0.001092s ]
[2025-08-06T05:19:49+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 11 LIMIT 1 [ RunTime:0.000816s ]
[2025-08-06T05:19:49+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '11' ORDER BY `oi`.`id` ASC [ RunTime:0.000712s ]
[2025-08-06T05:20:19+08:00][info] Loading API routes
[2025-08-06T05:20:19+08:00][sql] CONNECT:[ UseTime:0.001328s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:20:19+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.004708s ]
[2025-08-06T05:20:19+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 11 LIMIT 1 [ RunTime:0.001501s ]
[2025-08-06T05:20:19+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.003470s ]
[2025-08-06T05:20:19+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '11' ORDER BY `oi`.`id` ASC [ RunTime:0.001678s ]
[2025-08-06T05:20:20+08:00][info] Loading API routes
[2025-08-06T05:20:20+08:00][sql] CONNECT:[ UseTime:0.001232s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:20:20+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.004313s ]
[2025-08-06T05:20:20+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 11 LIMIT 1 [ RunTime:0.001160s ]
[2025-08-06T05:20:20+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.003868s ]
[2025-08-06T05:20:20+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '11' ORDER BY `oi`.`id` ASC [ RunTime:0.001445s ]
[2025-08-06T05:20:50+08:00][info] Loading API routes
[2025-08-06T05:20:50+08:00][sql] CONNECT:[ UseTime:0.001292s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:20:50+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.003496s ]
[2025-08-06T05:20:50+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 11 LIMIT 1 [ RunTime:0.001001s ]
[2025-08-06T05:20:50+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.002619s ]
[2025-08-06T05:20:50+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '11' ORDER BY `oi`.`id` ASC [ RunTime:0.000915s ]
[2025-08-06T05:20:50+08:00][info] Loading API routes
[2025-08-06T05:20:50+08:00][sql] CONNECT:[ UseTime:0.001264s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:20:50+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.003385s ]
[2025-08-06T05:20:50+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 11 LIMIT 1 [ RunTime:0.000587s ]
[2025-08-06T05:20:50+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.002381s ]
[2025-08-06T05:20:50+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '11' ORDER BY `oi`.`id` ASC [ RunTime:0.000654s ]
[2025-08-06T05:21:20+08:00][info] Loading API routes
[2025-08-06T05:21:20+08:00][sql] CONNECT:[ UseTime:0.001604s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:21:20+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.003531s ]
[2025-08-06T05:21:20+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 11 LIMIT 1 [ RunTime:0.000732s ]
[2025-08-06T05:21:20+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.002451s ]
[2025-08-06T05:21:20+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '11' ORDER BY `oi`.`id` ASC [ RunTime:0.000660s ]
[2025-08-06T05:21:20+08:00][info] Loading API routes
[2025-08-06T05:21:20+08:00][sql] CONNECT:[ UseTime:0.001207s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:21:20+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.003709s ]
[2025-08-06T05:21:20+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 11 LIMIT 1 [ RunTime:0.000615s ]
[2025-08-06T05:21:20+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.002394s ]
[2025-08-06T05:21:20+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '11' ORDER BY `oi`.`id` ASC [ RunTime:0.000635s ]
[2025-08-06T05:30:11+08:00][info] Loading API routes
[2025-08-06T05:30:33+08:00][info] Loading API routes
[2025-08-06T05:30:33+08:00][sql] CONNECT:[ UseTime:0.001550s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:30:33+08:00][sql] SHOW FULL COLUMNS FROM `agiso_orders` [ RunTime:0.003514s ]
[2025-08-06T05:30:33+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59' [ RunTime:0.000878s ]
[2025-08-06T05:30:33+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0 [ RunTime:0.000736s ]
[2025-08-06T05:30:33+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0  AND `processed` = 1 [ RunTime:0.000831s ]
[2025-08-06T05:30:33+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0  AND `processed` = 1  AND `processed` = 2 [ RunTime:0.000594s ]
[2025-08-06T05:30:33+08:00][sql] SELECT SUM(`payment`) AS think_sum FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0  AND `processed` = 1  AND `processed` = 2 [ RunTime:0.000598s ]
[2025-08-06T05:30:33+08:00][sql] SELECT platform, platform_name, COUNT(*) as count, SUM(payment) as amount FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59' GROUP BY `platform`,`platform_name` [ RunTime:0.000733s ]
[2025-08-06T05:30:33+08:00][sql] SELECT DATE(created_at) as date, COUNT(*) as count, SUM(payment) as amount FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59' GROUP BY DATE(created_at) ORDER BY `date` ASC [ RunTime:0.001031s ]
[2025-08-06T05:30:33+08:00][info] Loading API routes
[2025-08-06T05:30:33+08:00][sql] CONNECT:[ UseTime:0.001337s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:30:33+08:00][sql] SHOW FULL COLUMNS FROM `agiso_orders` [ RunTime:0.003726s ]
[2025-08-06T05:30:33+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` [ RunTime:0.000718s ]
[2025-08-06T05:30:33+08:00][sql] SELECT * FROM `agiso_orders` ORDER BY `created_at` DESC LIMIT 0,20 [ RunTime:0.000710s ]
[2025-08-06T05:30:33+08:00][info] Loading API routes
[2025-08-06T05:30:33+08:00][sql] CONNECT:[ UseTime:0.001267s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:30:33+08:00][sql] SHOW FULL COLUMNS FROM `agiso_orders` [ RunTime:0.003211s ]
[2025-08-06T05:30:33+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59' [ RunTime:0.000750s ]
[2025-08-06T05:30:33+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0 [ RunTime:0.000542s ]
[2025-08-06T05:30:33+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0  AND `processed` = 1 [ RunTime:0.000475s ]
[2025-08-06T05:30:33+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0  AND `processed` = 1  AND `processed` = 2 [ RunTime:0.000506s ]
[2025-08-06T05:30:33+08:00][sql] SELECT SUM(`payment`) AS think_sum FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59'  AND `processed` = 0  AND `processed` = 1  AND `processed` = 2 [ RunTime:0.000487s ]
[2025-08-06T05:30:33+08:00][sql] SELECT platform, platform_name, COUNT(*) as count, SUM(payment) as amount FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59' GROUP BY `platform`,`platform_name` [ RunTime:0.000688s ]
[2025-08-06T05:30:33+08:00][sql] SELECT DATE(created_at) as date, COUNT(*) as count, SUM(payment) as amount FROM `agiso_orders` WHERE  `created_at` >= '2025-07-30 00:00:00'  AND `created_at` <= '2025-08-06 23:59:59' GROUP BY DATE(created_at) ORDER BY `date` ASC [ RunTime:0.000630s ]
[2025-08-06T05:30:33+08:00][info] Loading API routes
[2025-08-06T05:30:33+08:00][sql] CONNECT:[ UseTime:0.001222s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:30:33+08:00][sql] SHOW FULL COLUMNS FROM `agiso_orders` [ RunTime:0.003297s ]
[2025-08-06T05:30:33+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` [ RunTime:0.000553s ]
[2025-08-06T05:30:33+08:00][sql] SELECT * FROM `agiso_orders` ORDER BY `created_at` DESC LIMIT 0,20 [ RunTime:0.000606s ]
[2025-08-06T05:30:33+08:00][info] Loading API routes
[2025-08-06T05:30:33+08:00][sql] CONNECT:[ UseTime:0.010985s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:30:33+08:00][sql] SHOW FULL COLUMNS FROM `agiso_orders` [ RunTime:0.003462s ]
[2025-08-06T05:30:33+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` [ RunTime:0.000598s ]
[2025-08-06T05:30:33+08:00][sql] SELECT * FROM `agiso_orders` ORDER BY `created_at` DESC LIMIT 0,20 [ RunTime:0.000627s ]
[2025-08-06T05:30:34+08:00][info] Loading API routes
[2025-08-06T05:30:34+08:00][sql] CONNECT:[ UseTime:0.011402s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:30:34+08:00][sql] SHOW FULL COLUMNS FROM `agiso_orders` [ RunTime:0.005249s ]
[2025-08-06T05:30:34+08:00][sql] SELECT COUNT(*) AS think_count FROM `agiso_orders` [ RunTime:0.000831s ]
[2025-08-06T05:30:34+08:00][sql] SELECT * FROM `agiso_orders` ORDER BY `created_at` DESC LIMIT 0,20 [ RunTime:0.000733s ]
[2025-08-06T05:30:34+08:00][info] Loading API routes
[2025-08-06T05:35:17+08:00][info] Loading API routes
[2025-08-06T05:35:17+08:00][sql] CONNECT:[ UseTime:0.001291s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:35:17+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.003583s ]
[2025-08-06T05:35:17+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 11 LIMIT 1 [ RunTime:0.000679s ]
[2025-08-06T05:35:17+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.002434s ]
[2025-08-06T05:35:17+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '11' ORDER BY `oi`.`id` ASC [ RunTime:0.000702s ]
[2025-08-06T05:35:17+08:00][info] Loading API routes
[2025-08-06T05:35:17+08:00][sql] CONNECT:[ UseTime:0.002157s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:35:17+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.004904s ]
[2025-08-06T05:35:17+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 11 LIMIT 1 [ RunTime:0.001049s ]
[2025-08-06T05:35:17+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.004853s ]
[2025-08-06T05:35:17+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '11' ORDER BY `oi`.`id` ASC [ RunTime:0.001450s ]
[2025-08-06T05:35:47+08:00][info] Loading API routes
[2025-08-06T05:35:47+08:00][sql] CONNECT:[ UseTime:0.011194s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:35:47+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.003640s ]
[2025-08-06T05:35:47+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 11 LIMIT 1 [ RunTime:0.001122s ]
[2025-08-06T05:35:47+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.004838s ]
[2025-08-06T05:35:47+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '11' ORDER BY `oi`.`id` ASC [ RunTime:0.002347s ]
[2025-08-06T05:35:47+08:00][info] Loading API routes
[2025-08-06T05:35:47+08:00][sql] CONNECT:[ UseTime:0.001260s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:35:47+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.003510s ]
[2025-08-06T05:35:47+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 11 LIMIT 1 [ RunTime:0.000845s ]
[2025-08-06T05:35:47+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.002487s ]
[2025-08-06T05:35:47+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '11' ORDER BY `oi`.`id` ASC [ RunTime:0.001458s ]
[2025-08-06T05:36:17+08:00][info] Loading API routes
[2025-08-06T05:36:17+08:00][sql] CONNECT:[ UseTime:0.001451s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:36:17+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.004653s ]
[2025-08-06T05:36:17+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 11 LIMIT 1 [ RunTime:0.001275s ]
[2025-08-06T05:36:17+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.003270s ]
[2025-08-06T05:36:17+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '11' ORDER BY `oi`.`id` ASC [ RunTime:0.001518s ]
[2025-08-06T05:36:17+08:00][info] Loading API routes
[2025-08-06T05:36:17+08:00][sql] CONNECT:[ UseTime:0.010637s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:36:17+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_orders` [ RunTime:0.003660s ]
[2025-08-06T05:36:17+08:00][sql] SELECT * FROM `restaurant_orders` WHERE  `id` = 11 LIMIT 1 [ RunTime:0.000802s ]
[2025-08-06T05:36:17+08:00][sql] SHOW FULL COLUMNS FROM `restaurant_order_items` [ RunTime:0.002423s ]
[2025-08-06T05:36:17+08:00][sql] SELECT `oi`.*,d.main_image as dish_image,`d`.`category_id`,`d`.`dish_type` FROM `restaurant_order_items` `oi` LEFT JOIN `dishes` `d` ON `oi`.`dish_id`=`d`.`id` WHERE  `oi`.`order_id` = '11' ORDER BY `oi`.`id` ASC [ RunTime:0.001167s ]
[2025-08-06T05:42:32+08:00][info] Loading API routes
[2025-08-06T05:42:32+08:00][sql] CONNECT:[ UseTime:0.001595s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:32+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.003031s ]
[2025-08-06T05:42:32+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.000727s ]
[2025-08-06T05:42:32+08:00][info] Loading API routes
[2025-08-06T05:42:32+08:00][sql] CONNECT:[ UseTime:0.001543s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:32+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.004532s ]
[2025-08-06T05:42:32+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.001530s ]
[2025-08-06T05:42:32+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000779s ]
[2025-08-06T05:42:43+08:00][info] Loading API routes
[2025-08-06T05:42:43+08:00][sql] CONNECT:[ UseTime:0.001132s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:43+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002554s ]
[2025-08-06T05:42:43+08:00][sql] SELECT * FROM `city_map` ORDER BY `first_char` ASC,`city_name` ASC [ RunTime:0.001153s ]
[2025-08-06T05:42:44+08:00][info] Loading API routes
[2025-08-06T05:42:44+08:00][sql] CONNECT:[ UseTime:0.001210s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:44+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002609s ]
[2025-08-06T05:42:44+08:00][sql] SELECT * FROM `city_map` WHERE  `city_code` = '110100' [ RunTime:0.001021s ]
[2025-08-06T05:42:44+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002306s ]
[2025-08-06T05:42:44+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.003009s ]
[2025-08-06T05:42:44+08:00][info] Loading API routes
[2025-08-06T05:42:44+08:00][sql] CONNECT:[ UseTime:0.001163s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:44+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002845s ]
[2025-08-06T05:42:44+08:00][sql] SELECT `city_code`,`city_name` FROM `city_map` WHERE  `city_code` = '110100' LIMIT 1 [ RunTime:0.000779s ]
[2025-08-06T05:42:44+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002094s ]
[2025-08-06T05:42:44+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.003981s ]
[2025-08-06T05:42:46+08:00][info] Loading API routes
[2025-08-06T05:42:46+08:00][sql] CONNECT:[ UseTime:0.001367s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:46+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.003721s ]
[2025-08-06T05:42:46+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.001317s ]
[2025-08-06T05:42:46+08:00][info] Loading API routes
[2025-08-06T05:42:46+08:00][sql] CONNECT:[ UseTime:0.001141s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:46+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002636s ]
[2025-08-06T05:42:46+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.000950s ]
[2025-08-06T05:42:46+08:00][info] Loading API routes
[2025-08-06T05:42:46+08:00][sql] CONNECT:[ UseTime:0.001334s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:46+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.004427s ]
[2025-08-06T05:42:46+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.001338s ]
[2025-08-06T05:42:46+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.008207s ]
[2025-08-06T05:42:48+08:00][info] Loading API routes
[2025-08-06T05:42:48+08:00][sql] CONNECT:[ UseTime:0.001194s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:48+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002855s ]
[2025-08-06T05:42:48+08:00][sql] SELECT * FROM `city_map` ORDER BY `first_char` ASC,`city_name` ASC [ RunTime:0.001308s ]
[2025-08-06T05:42:48+08:00][info] Loading API routes
[2025-08-06T05:42:48+08:00][sql] CONNECT:[ UseTime:0.001222s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:48+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002849s ]
[2025-08-06T05:42:48+08:00][sql] SELECT * FROM `city_map` WHERE  `city_code` = '110100' [ RunTime:0.001104s ]
[2025-08-06T05:42:48+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.003431s ]
[2025-08-06T05:42:48+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.004280s ]
[2025-08-06T05:42:48+08:00][info] Loading API routes
[2025-08-06T05:42:48+08:00][sql] CONNECT:[ UseTime:0.001170s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:48+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002456s ]
[2025-08-06T05:42:48+08:00][sql] SELECT `city_code`,`city_name` FROM `city_map` WHERE  `city_code` = '110100' LIMIT 1 [ RunTime:0.000709s ]
[2025-08-06T05:42:48+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002134s ]
[2025-08-06T05:42:48+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.003453s ]
[2025-08-06T05:42:50+08:00][info] Loading API routes
[2025-08-06T05:42:50+08:00][sql] CONNECT:[ UseTime:0.001312s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:50+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.003803s ]
[2025-08-06T05:42:50+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.001387s ]
[2025-08-06T05:42:50+08:00][info] Loading API routes
[2025-08-06T05:42:50+08:00][sql] CONNECT:[ UseTime:0.001073s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:50+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002665s ]
[2025-08-06T05:42:50+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.000869s ]
[2025-08-06T05:42:50+08:00][info] Loading API routes
[2025-08-06T05:42:50+08:00][sql] CONNECT:[ UseTime:0.002179s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:50+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.004115s ]
[2025-08-06T05:42:50+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.001082s ]
[2025-08-06T05:42:50+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000491s ]
[2025-08-06T05:42:56+08:00][info] Loading API routes
[2025-08-06T05:42:56+08:00][sql] CONNECT:[ UseTime:0.001299s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:42:56+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.003773s ]
[2025-08-06T05:43:39+08:00][info] Loading API routes
[2025-08-06T05:43:39+08:00][sql] CONNECT:[ UseTime:0.001595s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:43:39+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.003494s ]
[2025-08-06T05:43:39+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.001119s ]
[2025-08-06T05:43:39+08:00][info] Loading API routes
[2025-08-06T05:43:39+08:00][sql] CONNECT:[ UseTime:0.001130s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:43:39+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.004222s ]
[2025-08-06T05:43:39+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.001022s ]
[2025-08-06T05:43:39+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000745s ]
[2025-08-06T05:43:40+08:00][info] Loading API routes
[2025-08-06T05:43:40+08:00][sql] CONNECT:[ UseTime:0.001671s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:43:40+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.003420s ]
[2025-08-06T05:43:40+08:00][sql] SELECT * FROM `city_map` ORDER BY `first_char` ASC,`city_name` ASC [ RunTime:0.001725s ]
[2025-08-06T05:43:40+08:00][info] Loading API routes
[2025-08-06T05:43:40+08:00][sql] CONNECT:[ UseTime:0.001256s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:43:40+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.003401s ]
[2025-08-06T05:43:40+08:00][sql] SELECT * FROM `city_map` WHERE  `city_code` = '110100' [ RunTime:0.001254s ]
[2025-08-06T05:43:40+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002773s ]
[2025-08-06T05:43:40+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.003198s ]
[2025-08-06T05:43:40+08:00][info] Loading API routes
[2025-08-06T05:43:40+08:00][sql] CONNECT:[ UseTime:0.001199s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:43:40+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002533s ]
[2025-08-06T05:43:40+08:00][sql] SELECT `city_code`,`city_name` FROM `city_map` WHERE  `city_code` = '110100' LIMIT 1 [ RunTime:0.000873s ]
[2025-08-06T05:43:40+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.003889s ]
[2025-08-06T05:43:40+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.005594s ]
[2025-08-06T05:43:45+08:00][info] Loading API routes
[2025-08-06T05:43:45+08:00][sql] CONNECT:[ UseTime:0.001212s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:43:45+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002903s ]
[2025-08-06T05:43:45+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.000751s ]
[2025-08-06T05:43:45+08:00][info] Loading API routes
[2025-08-06T05:43:45+08:00][sql] CONNECT:[ UseTime:0.001256s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:43:45+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002595s ]
[2025-08-06T05:43:45+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.000668s ]
[2025-08-06T05:43:45+08:00][info] Loading API routes
[2025-08-06T05:43:45+08:00][sql] CONNECT:[ UseTime:0.001160s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:43:45+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.003342s ]
[2025-08-06T05:43:45+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.000821s ]
[2025-08-06T05:43:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000471s ]
[2025-08-06T05:44:02+08:00][info] Loading API routes
[2025-08-06T05:44:02+08:00][sql] CONNECT:[ UseTime:0.001095s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:02+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002563s ]
[2025-08-06T05:44:02+08:00][sql] SELECT * FROM `city_map` ORDER BY `first_char` ASC,`city_name` ASC [ RunTime:0.001243s ]
[2025-08-06T05:44:02+08:00][info] Loading API routes
[2025-08-06T05:44:02+08:00][sql] CONNECT:[ UseTime:0.001170s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:02+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002725s ]
[2025-08-06T05:44:02+08:00][sql] SELECT * FROM `city_map` WHERE  `city_code` = '110100' [ RunTime:0.000751s ]
[2025-08-06T05:44:02+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002163s ]
[2025-08-06T05:44:02+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.003148s ]
[2025-08-06T05:44:02+08:00][info] Loading API routes
[2025-08-06T05:44:02+08:00][sql] CONNECT:[ UseTime:0.001389s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:02+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.003353s ]
[2025-08-06T05:44:02+08:00][sql] SELECT `city_code`,`city_name` FROM `city_map` WHERE  `city_code` = '110100' LIMIT 1 [ RunTime:0.000590s ]
[2025-08-06T05:44:02+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002151s ]
[2025-08-06T05:44:02+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.002995s ]
[2025-08-06T05:44:03+08:00][info] Loading API routes
[2025-08-06T05:44:03+08:00][sql] CONNECT:[ UseTime:0.001220s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:03+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002842s ]
[2025-08-06T05:44:03+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '2' LIMIT 1 [ RunTime:0.000875s ]
[2025-08-06T05:44:04+08:00][info] Loading API routes
[2025-08-06T05:44:04+08:00][sql] CONNECT:[ UseTime:0.010713s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:04+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002838s ]
[2025-08-06T05:44:04+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '2' LIMIT 1 [ RunTime:0.000811s ]
[2025-08-06T05:44:04+08:00][info] Loading API routes
[2025-08-06T05:44:04+08:00][sql] CONNECT:[ UseTime:0.001376s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:04+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.004632s ]
[2025-08-06T05:44:04+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.001059s ]
[2025-08-06T05:44:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000938s ]
[2025-08-06T05:44:06+08:00][info] Loading API routes
[2025-08-06T05:44:06+08:00][sql] CONNECT:[ UseTime:0.001131s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:06+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002451s ]
[2025-08-06T05:44:06+08:00][sql] SELECT * FROM `city_map` ORDER BY `first_char` ASC,`city_name` ASC [ RunTime:0.001220s ]
[2025-08-06T05:44:06+08:00][info] Loading API routes
[2025-08-06T05:44:06+08:00][sql] CONNECT:[ UseTime:0.001142s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:06+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.003482s ]
[2025-08-06T05:44:06+08:00][sql] SELECT * FROM `city_map` WHERE  `city_code` = '110100' [ RunTime:0.000895s ]
[2025-08-06T05:44:06+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002668s ]
[2025-08-06T05:44:06+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.003217s ]
[2025-08-06T05:44:06+08:00][info] Loading API routes
[2025-08-06T05:44:06+08:00][sql] CONNECT:[ UseTime:0.001210s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:06+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.003057s ]
[2025-08-06T05:44:06+08:00][sql] SELECT `city_code`,`city_name` FROM `city_map` WHERE  `city_code` = '110100' LIMIT 1 [ RunTime:0.000772s ]
[2025-08-06T05:44:06+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.003283s ]
[2025-08-06T05:44:06+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.004573s ]
[2025-08-06T05:44:08+08:00][info] Loading API routes
[2025-08-06T05:44:08+08:00][sql] CONNECT:[ UseTime:0.001190s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:08+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002985s ]
[2025-08-06T05:44:08+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '3' LIMIT 1 [ RunTime:0.000967s ]
[2025-08-06T05:44:08+08:00][info] Loading API routes
[2025-08-06T05:44:08+08:00][sql] CONNECT:[ UseTime:0.001076s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:08+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002444s ]
[2025-08-06T05:44:08+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '3' LIMIT 1 [ RunTime:0.001054s ]
[2025-08-06T05:44:09+08:00][info] Loading API routes
[2025-08-06T05:44:09+08:00][sql] CONNECT:[ UseTime:0.001084s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:09+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.003195s ]
[2025-08-06T05:44:09+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.000868s ]
[2025-08-06T05:44:09+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000473s ]
[2025-08-06T05:44:10+08:00][info] Loading API routes
[2025-08-06T05:44:10+08:00][sql] CONNECT:[ UseTime:0.001761s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:10+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.003906s ]
[2025-08-06T05:44:10+08:00][sql] SELECT * FROM `city_map` ORDER BY `first_char` ASC,`city_name` ASC [ RunTime:0.001940s ]
[2025-08-06T05:44:11+08:00][info] Loading API routes
[2025-08-06T05:44:11+08:00][sql] CONNECT:[ UseTime:0.003060s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:11+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.007057s ]
[2025-08-06T05:44:11+08:00][sql] SELECT * FROM `city_map` WHERE  `city_code` = '110100' [ RunTime:0.001174s ]
[2025-08-06T05:44:11+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.004527s ]
[2025-08-06T05:44:11+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.006854s ]
[2025-08-06T05:44:11+08:00][info] Loading API routes
[2025-08-06T05:44:11+08:00][sql] CONNECT:[ UseTime:0.001514s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:11+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.004592s ]
[2025-08-06T05:44:11+08:00][sql] SELECT `city_code`,`city_name` FROM `city_map` WHERE  `city_code` = '110100' LIMIT 1 [ RunTime:0.000974s ]
[2025-08-06T05:44:11+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.003249s ]
[2025-08-06T05:44:11+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.004174s ]
[2025-08-06T05:44:14+08:00][info] Loading API routes
[2025-08-06T05:44:14+08:00][sql] CONNECT:[ UseTime:0.001400s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:14+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002901s ]
[2025-08-06T05:44:14+08:00][sql] SELECT * FROM `city_map` ORDER BY `first_char` ASC,`city_name` ASC [ RunTime:0.001589s ]
[2025-08-06T05:44:15+08:00][info] Loading API routes
[2025-08-06T05:44:15+08:00][sql] CONNECT:[ UseTime:0.001263s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:15+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.003072s ]
[2025-08-06T05:44:15+08:00][sql] SELECT * FROM `city_map` ORDER BY `first_char` ASC,`city_name` ASC [ RunTime:0.001564s ]
[2025-08-06T05:44:15+08:00][info] Loading API routes
[2025-08-06T05:44:15+08:00][sql] CONNECT:[ UseTime:0.001242s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:15+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002993s ]
[2025-08-06T05:44:15+08:00][sql] SELECT * FROM `city_map` WHERE  `city_code` = '152900' [ RunTime:0.001092s ]
[2025-08-06T05:44:15+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002757s ]
[2025-08-06T05:44:15+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '152900' ORDER BY `id` ASC [ RunTime:0.003515s ]
[2025-08-06T05:44:15+08:00][info] Loading API routes
[2025-08-06T05:44:16+08:00][sql] CONNECT:[ UseTime:0.001162s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:16+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002420s ]
[2025-08-06T05:44:16+08:00][sql] SELECT `city_code`,`city_name` FROM `city_map` WHERE  `city_code` = '152900' LIMIT 1 [ RunTime:0.000442s ]
[2025-08-06T05:44:16+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.001991s ]
[2025-08-06T05:44:16+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '152900' ORDER BY `id` ASC [ RunTime:0.003899s ]
[2025-08-06T05:44:16+08:00][info] Loading API routes
[2025-08-06T05:44:16+08:00][sql] CONNECT:[ UseTime:0.001110s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:16+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002463s ]
[2025-08-06T05:44:16+08:00][sql] SELECT * FROM `city_map` ORDER BY `first_char` ASC,`city_name` ASC [ RunTime:0.001193s ]
[2025-08-06T05:44:16+08:00][info] Loading API routes
[2025-08-06T05:44:16+08:00][sql] CONNECT:[ UseTime:0.001117s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:16+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002405s ]
[2025-08-06T05:44:16+08:00][sql] SELECT * FROM `city_map` WHERE  `city_code` = '152900' [ RunTime:0.000674s ]
[2025-08-06T05:44:16+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002274s ]
[2025-08-06T05:44:16+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '152900' ORDER BY `id` ASC [ RunTime:0.002974s ]
[2025-08-06T05:44:16+08:00][info] Loading API routes
[2025-08-06T05:44:16+08:00][sql] CONNECT:[ UseTime:0.001123s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:16+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.002593s ]
[2025-08-06T05:44:16+08:00][sql] SELECT `city_code`,`city_name` FROM `city_map` WHERE  `city_code` = '152900' LIMIT 1 [ RunTime:0.000596s ]
[2025-08-06T05:44:16+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002497s ]
[2025-08-06T05:44:16+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '152900' ORDER BY `id` ASC [ RunTime:0.003707s ]
[2025-08-06T05:44:18+08:00][info] Loading API routes
[2025-08-06T05:44:18+08:00][sql] CONNECT:[ UseTime:0.001114s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:18+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002783s ]
[2025-08-06T05:44:18+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '448' LIMIT 1 [ RunTime:0.000936s ]
[2025-08-06T05:44:19+08:00][info] Loading API routes
[2025-08-06T05:44:19+08:00][sql] CONNECT:[ UseTime:0.001021s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:19+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.003000s ]
[2025-08-06T05:44:19+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '448' LIMIT 1 [ RunTime:0.000608s ]
[2025-08-06T05:44:19+08:00][info] Loading API routes
[2025-08-06T05:44:19+08:00][sql] CONNECT:[ UseTime:0.001243s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:19+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.003223s ]
[2025-08-06T05:44:19+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.001107s ]
[2025-08-06T05:44:19+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000588s ]
[2025-08-06T05:44:21+08:00][info] Loading API routes
[2025-08-06T05:44:21+08:00][sql] CONNECT:[ UseTime:0.004422s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:21+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.005113s ]
[2025-08-06T05:44:21+08:00][sql] SELECT * FROM `city_map` ORDER BY `first_char` ASC,`city_name` ASC [ RunTime:0.002023s ]
[2025-08-06T05:44:22+08:00][info] Loading API routes
[2025-08-06T05:44:22+08:00][sql] CONNECT:[ UseTime:0.001593s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:22+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.007082s ]
[2025-08-06T05:44:22+08:00][sql] SELECT * FROM `city_map` WHERE  `city_code` = '110100' [ RunTime:0.001192s ]
[2025-08-06T05:44:22+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.004547s ]
[2025-08-06T05:44:22+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.009991s ]
[2025-08-06T05:44:22+08:00][info] Loading API routes
[2025-08-06T05:44:22+08:00][sql] CONNECT:[ UseTime:0.001388s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:22+08:00][sql] SHOW FULL COLUMNS FROM `city_map` [ RunTime:0.003646s ]
[2025-08-06T05:44:22+08:00][sql] SELECT `city_code`,`city_name` FROM `city_map` WHERE  `city_code` = '110100' LIMIT 1 [ RunTime:0.000682s ]
[2025-08-06T05:44:22+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002892s ]
[2025-08-06T05:44:22+08:00][sql] SELECT * FROM `store_info` WHERE  `city_code` = '110100' ORDER BY `id` ASC [ RunTime:0.005359s ]
[2025-08-06T05:44:23+08:00][info] Loading API routes
[2025-08-06T05:44:23+08:00][sql] CONNECT:[ UseTime:0.002368s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:23+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.004578s ]
[2025-08-06T05:44:23+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.001409s ]
[2025-08-06T05:44:24+08:00][info] Loading API routes
[2025-08-06T05:44:24+08:00][sql] CONNECT:[ UseTime:0.001094s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:24+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002729s ]
[2025-08-06T05:44:24+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.000696s ]
[2025-08-06T05:44:24+08:00][info] Loading API routes
[2025-08-06T05:44:24+08:00][sql] CONNECT:[ UseTime:0.001278s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:44:24+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.003139s ]
[2025-08-06T05:44:24+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.000903s ]
[2025-08-06T05:44:24+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000786s ]
[2025-08-06T05:48:19+08:00][info] Loading API routes
[2025-08-06T05:48:19+08:00][sql] CONNECT:[ UseTime:0.001365s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:48:19+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002891s ]
[2025-08-06T05:48:19+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.000884s ]
[2025-08-06T05:48:19+08:00][info] Loading API routes
[2025-08-06T05:48:19+08:00][sql] CONNECT:[ UseTime:0.001204s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:48:19+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.003353s ]
[2025-08-06T05:48:19+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.000893s ]
[2025-08-06T05:48:19+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000559s ]
[2025-08-06T05:48:36+08:00][info] Loading API routes
[2025-08-06T05:48:36+08:00][sql] CONNECT:[ UseTime:0.001214s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:48:36+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002805s ]
[2025-08-06T05:48:36+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.000756s ]
[2025-08-06T05:48:36+08:00][info] Loading API routes
[2025-08-06T05:48:36+08:00][sql] CONNECT:[ UseTime:0.001139s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:48:36+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.003221s ]
[2025-08-06T05:48:36+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.001063s ]
[2025-08-06T05:48:36+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000583s ]
[2025-08-06T05:49:24+08:00][info] Loading API routes
[2025-08-06T05:49:24+08:00][sql] CONNECT:[ UseTime:0.001261s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:49:24+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.003437s ]
[2025-08-06T05:49:24+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.000766s ]
[2025-08-06T05:49:24+08:00][info] Loading API routes
[2025-08-06T05:49:24+08:00][sql] CONNECT:[ UseTime:0.001155s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:49:24+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.003153s ]
[2025-08-06T05:49:24+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.000951s ]
[2025-08-06T05:49:24+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000753s ]
[2025-08-06T05:49:45+08:00][info] Loading API routes
[2025-08-06T05:49:45+08:00][sql] CONNECT:[ UseTime:0.001276s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:49:45+08:00][sql] SHOW FULL COLUMNS FROM `store_info` [ RunTime:0.002791s ]
[2025-08-06T05:49:45+08:00][sql] SELECT `s`.*,`c`.`city_name`,`c`.`city_pinyin` FROM `store_info` `s` LEFT JOIN `city_map` `c` ON `s`.`city_code`=`c`.`city_code` WHERE  `s`.`id` = '1' LIMIT 1 [ RunTime:0.000691s ]
[2025-08-06T05:49:45+08:00][info] Loading API routes
[2025-08-06T05:49:45+08:00][sql] CONNECT:[ UseTime:0.001095s ] mysql:host=127.0.0.1;port=3306;dbname=orderflow;charset=utf8mb4
[2025-08-06T05:49:45+08:00][sql] SHOW FULL COLUMNS FROM `dishes` [ RunTime:0.003118s ]
[2025-08-06T05:49:45+08:00][sql] SELECT `d`.*,c.name as category_name FROM `dishes` `d` LEFT JOIN `dish_categories` `c` ON `d`.`category_id`=`c`.`id` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) ORDER BY `d`.`sort_order` ASC,`d`.`sales` DESC,`d`.`id` DESC LIMIT 0,50 [ RunTime:0.000793s ]
[2025-08-06T05:49:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `dishes` `d` WHERE  (  `d`.`status` = '1'  AND `d`.`is_available` = '1' ) [ RunTime:0.000692s ]
