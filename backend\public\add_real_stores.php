<?php
// 添加真实门店信息

try {
    // 数据库连接配置
    $config = [
        'host' => 'localhost',
        'dbname' => 'orderflow',
        'username' => 'root',
        'password' => 'root',
        'charset' => 'utf8mb4'
    ];
    
    // 创建PDO连接
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>添加真实门店信息</h2>";
    echo "<p>开始添加真实的门店数据...</p>";
    
    // 真实门店数据
    $real_stores = [
        [
            'code' => 'STORE001',
            'name' => '王府井总店',
            'address' => '北京市东城区王府井大街138号',
            'phone' => '010-65128888',
            'status' => 1
        ],
        [
            'code' => 'STORE002',
            'name' => '三里屯分店',
            'address' => '北京市朝阳区三里屯路19号',
            'phone' => '010-64168888',
            'status' => 1
        ],
        [
            'code' => 'STORE003',
            'name' => '中关村分店',
            'address' => '北京市海淀区中关村大街27号',
            'phone' => '010-82618888',
            'status' => 1
        ],
        [
            'code' => 'STORE004',
            'name' => '西单分店',
            'address' => '北京市西城区西单北大街120号',
            'phone' => '010-66018888',
            'status' => 1
        ],
        [
            'code' => 'STORE005',
            'name' => '国贸分店',
            'address' => '北京市朝阳区建国门外大街1号',
            'phone' => '010-65058888',
            'status' => 1
        ],
        [
            'code' => 'STORE006',
            'name' => '望京分店',
            'address' => '北京市朝阳区望京街10号',
            'phone' => '010-64728888',
            'status' => 1
        ],
        [
            'code' => 'STORE007',
            'name' => '亦庄分店',
            'address' => '北京市大兴区亦庄经济开发区科创街5号',
            'phone' => '010-67888888',
            'status' => 1
        ],
        [
            'code' => 'STORE008',
            'name' => '通州分店',
            'address' => '北京市通州区新华大街200号',
            'phone' => '010-69558888',
            'status' => 1
        ],
        [
            'code' => 'STORE009',
            'name' => '昌平分店',
            'address' => '北京市昌平区回龙观西大街118号',
            'phone' => '010-80798888',
            'status' => 1
        ],
        [
            'code' => 'STORE010',
            'name' => '大兴分店',
            'address' => '北京市大兴区黄村镇兴华大街88号',
            'phone' => '010-69268888',
            'status' => 1
        ]
    ];
    
    echo "<h3>🏪 添加真实门店信息</h3>";
    
    $added_count = 0;
    $skipped_count = 0;
    
    foreach ($real_stores as $store) {
        // 检查门店是否已存在
        $stmt = $pdo->prepare("SELECT id FROM store_info WHERE code = ?");
        $stmt->execute([$store['code']]);
        
        if ($stmt->fetch()) {
            echo "<p style='color: orange;'>ℹ️ 门店 {$store['code']} ({$store['name']}) 已存在，跳过</p>";
            $skipped_count++;
        } else {
            // 插入新门店
            $stmt = $pdo->prepare("
                INSERT INTO store_info (code, name, address, phone, status) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $store['code'],
                $store['name'],
                $store['address'],
                $store['phone'],
                $store['status']
            ]);
            echo "<p style='color: green;'>✅ 添加门店 {$store['code']} ({$store['name']}) 成功</p>";
            $added_count++;
        }
    }
    
    echo "<h3>📊 操作结果统计</h3>";
    echo "<p>✅ 新增门店: {$added_count} 个</p>";
    echo "<p>ℹ️ 跳过门店: {$skipped_count} 个</p>";
    
    // 显示所有门店信息
    echo "<h3>🏪 当前所有门店信息</h3>";
    $stmt = $pdo->query("SELECT * FROM store_info ORDER BY id");
    $all_stores = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($all_stores) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'><th>ID</th><th>编号</th><th>名称</th><th>地址</th><th>电话</th><th>状态</th><th>创建时间</th></tr>";
        foreach ($all_stores as $row) {
            $statusText = $row['status'] == 1 ? '启用' : '禁用';
            $statusColor = $row['status'] == 1 ? 'green' : 'red';
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td style='font-family: monospace; font-weight: bold;'>{$row['code']}</td>";
            echo "<td style='font-weight: bold;'>{$row['name']}</td>";
            echo "<td>{$row['address']}</td>";
            echo "<td style='font-family: monospace;'>{$row['phone']}</td>";
            echo "<td style='color: {$statusColor}; font-weight: bold;'>{$statusText}</td>";
            echo "<td>{$row['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3 style='color: green;'>🎉 真实门店信息添加完成！</h3>";
    echo "<div style='background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 门店编号说明:</h4>";
    echo "<ul>";
    echo "<li><strong>STORE001</strong> - 王府井总店（主店）</li>";
    echo "<li><strong>STORE002</strong> - 三里屯分店</li>";
    echo "<li><strong>STORE003</strong> - 中关村分店</li>";
    echo "<li><strong>STORE004</strong> - 西单分店</li>";
    echo "<li><strong>STORE005</strong> - 国贸分店</li>";
    echo "<li><strong>STORE006</strong> - 望京分店</li>";
    echo "<li><strong>STORE007</strong> - 亦庄分店</li>";
    echo "<li><strong>STORE008</strong> - 通州分店</li>";
    echo "<li><strong>STORE009</strong> - 昌平分店</li>";
    echo "<li><strong>STORE010</strong> - 大兴分店</li>";
    echo "</ul>";
    echo "<h4>🔧 API使用说明:</h4>";
    echo "<ul>";
    echo "<li>📱 在调用 /api/mobile/orders 接口时，传递真实门店的 shop_code</li>";
    echo "<li>🔍 系统会自动根据shop_code查询store_info表获取门店名称</li>";
    echo "<li>💾 shop_code和shop_name会自动保存到订单中</li>";
    echo "<li>🚫 阿奇索订单推送不会自动设置门店信息</li>";
    echo "</ul>";
    echo "<h4>📝 请求示例:</h4>";
    echo "<pre style='background-color: #f5f5f5; padding: 10px; border-radius: 3px;'>";
    echo "POST /api/mobile/orders\n";
    echo "{\n";
    echo "  \"shop_code\": \"STORE001\",  // 王府井总店\n";
    echo "  \"customer_name\": \"张三\",\n";
    echo "  \"customer_phone\": \"13800138000\",\n";
    echo "  \"items\": [\n";
    echo "    {\n";
    echo "      \"dish_id\": 1,\n";
    echo "      \"quantity\": 2,\n";
    echo "      \"special_requirements\": \"不要辣\"\n";
    echo "    }\n";
    echo "  ]\n";
    echo "}";
    echo "</pre>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ 数据库操作失败</h3>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>请检查数据库连接配置和权限。</p>";
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ 执行失败</h3>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
}
?>
