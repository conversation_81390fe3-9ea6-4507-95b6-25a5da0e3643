<?php
// 移除阿奇索相关的虚拟店铺，保留真实门店

try {
    // 数据库连接配置
    $config = [
        'host' => 'localhost',
        'dbname' => 'orderflow',
        'username' => 'root',
        'password' => 'root',
        'charset' => 'utf8mb4'
    ];
    
    // 创建PDO连接
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>移除阿奇索虚拟店铺，保留真实门店</h2>";
    echo "<p>开始清理阿奇索相关的虚拟店铺...</p>";
    
    // 显示当前所有店铺
    echo "<h3>📋 当前所有店铺信息</h3>";
    $stmt = $pdo->query("SELECT * FROM store_info ORDER BY id");
    $all_stores = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($all_stores) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'><th>ID</th><th>编号</th><th>名称</th><th>地址</th><th>电话</th><th>状态</th><th>类型</th></tr>";
        foreach ($all_stores as $row) {
            $statusText = $row['status'] == 1 ? '启用' : '禁用';
            $statusColor = $row['status'] == 1 ? 'green' : 'red';
            $isAgiso = strpos($row['code'], 'AGISO_') === 0;
            $bgColor = $isAgiso ? 'background-color: #ffe6e6;' : 'background-color: #e6ffe6;';
            $typeText = $isAgiso ? '阿奇索虚拟店铺' : '真实门店';
            echo "<tr style='{$bgColor}'>";
            echo "<td>{$row['id']}</td>";
            echo "<td style='font-family: monospace;'>{$row['code']}</td>";
            echo "<td>{$row['name']}</td>";
            echo "<td>{$row['address']}</td>";
            echo "<td>{$row['phone']}</td>";
            echo "<td style='color: {$statusColor};'>{$statusText}</td>";
            echo "<td>{$typeText}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p style='color: #666; font-size: 12px;'>💡 红色背景：阿奇索虚拟店铺（将被删除），绿色背景：真实门店（保留）</p>";
    }
    
    // 查找阿奇索相关店铺
    echo "<h3>🔍 查找阿奇索相关店铺</h3>";
    $stmt = $pdo->query("SELECT * FROM store_info WHERE code LIKE 'AGISO_%'");
    $agiso_stores = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($agiso_stores)) {
        echo "<p style='color: orange;'>ℹ️ 没有找到阿奇索相关店铺</p>";
    } else {
        echo "<p>找到 " . count($agiso_stores) . " 个阿奇索相关店铺：</p>";
        echo "<ul>";
        foreach ($agiso_stores as $store) {
            echo "<li><strong>{$store['code']}</strong> - {$store['name']}</li>";
        }
        echo "</ul>";
        
        // 删除阿奇索相关店铺
        echo "<h3>🗑️ 删除阿奇索虚拟店铺</h3>";
        $stmt = $pdo->prepare("DELETE FROM store_info WHERE code LIKE 'AGISO_%'");
        $deleted_count = $stmt->execute();
        $affected_rows = $stmt->rowCount();
        
        echo "<p style='color: green;'>✅ 成功删除 {$affected_rows} 个阿奇索虚拟店铺</p>";
    }
    
    // 显示清理后的店铺列表
    echo "<h3>🏪 清理后的门店列表</h3>";
    $stmt = $pdo->query("SELECT * FROM store_info ORDER BY id");
    $remaining_stores = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($remaining_stores) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'><th>ID</th><th>编号</th><th>名称</th><th>地址</th><th>电话</th><th>状态</th><th>创建时间</th></tr>";
        foreach ($remaining_stores as $row) {
            $statusText = $row['status'] == 1 ? '启用' : '禁用';
            $statusColor = $row['status'] == 1 ? 'green' : 'red';
            echo "<tr style='background-color: #e6ffe6;'>";
            echo "<td>{$row['id']}</td>";
            echo "<td style='font-family: monospace;'>{$row['code']}</td>";
            echo "<td>{$row['name']}</td>";
            echo "<td>{$row['address']}</td>";
            echo "<td>{$row['phone']}</td>";
            echo "<td style='color: {$statusColor};'>{$statusText}</td>";
            echo "<td>{$row['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ 没有剩余的门店信息</p>";
    }
    
    echo "<h3 style='color: green;'>🎉 清理完成！</h3>";
    echo "<div style='background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 清理结果:</h4>";
    echo "<ul>";
    echo "<li>✅ 删除了所有阿奇索虚拟店铺（AGISO_*）</li>";
    echo "<li>✅ 保留了所有真实门店信息</li>";
    echo "<li>✅ 阿奇索订单推送不再自动映射店铺</li>";
    echo "</ul>";
    echo "<h4>🔧 使用说明:</h4>";
    echo "<ul>";
    echo "<li>📱 手动订单提交时，可以传递真实门店的 shop_code</li>";
    echo "<li>🔗 阿奇索订单推送时，不会自动设置店铺信息</li>";
    echo "<li>🏪 如需为阿奇索订单指定门店，需要在业务逻辑中手动处理</li>";
    echo "</ul>";
    echo "<h4>📝 API使用示例:</h4>";
    echo "<pre style='background-color: #f5f5f5; padding: 10px; border-radius: 3px;'>";
    echo "POST /api/mobile/orders\n";
    echo "{\n";
    echo "  \"shop_code\": \"SHOP001\",  // 真实门店编号\n";
    echo "  \"customer_name\": \"张三\",\n";
    echo "  \"customer_phone\": \"13800138000\",\n";
    echo "  \"items\": [...]\n";
    echo "}";
    echo "</pre>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ 数据库操作失败</h3>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>请检查数据库连接配置和权限。</p>";
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ 执行失败</h3>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
}
?>
