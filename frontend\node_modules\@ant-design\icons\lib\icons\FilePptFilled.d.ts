import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![file-ppt](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC43YzYgNiA5LjQgMTQuMSA5LjQgMjIuNlY5MjhjMCAxNy43LTE0LjMgMzItMzIgMzJIMTkyYy0xNy43IDAtMzItMTQuMy0zMi0zMlY5NmMwLTE3LjcgMTQuMy0zMiAzMi0zMmg0MjQuN2M4LjUgMCAxNi43IDMuNCAyMi43IDkuNGwyMTUuMiAyMTUuM3pNNzkwLjIgMzI2TDYwMiAxMzcuOFYzMjZoMTg4LjJ6TTQ2OC41MyA3NjB2LTkxLjU0aDU5LjI3YzYwLjU3IDAgMTAwLjItMzkuNjUgMTAwLjItOTguMTIgMC01OC4yMi0zOS41OC05OC4zNC05OS45OC05OC4zNEg0MjRhMTIgMTIgMCAwMC0xMiAxMnYyNzZhMTIgMTIgMCAwMDEyIDEyaDMyLjUzYTEyIDEyIDAgMDAxMi0xMnptMC0xMzkuMzNoMzQuOWM0Ny44MiAwIDY3LjE5LTEyLjkzIDY3LjE5LTUwLjMzIDAtMzIuMDUtMTguMTItNTAuMTItNDkuODctNTAuMTJoLTUyLjIydjEwMC40NXoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
