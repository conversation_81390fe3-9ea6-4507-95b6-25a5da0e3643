<?php
// 检查订单相关表的结构

try {
    // 数据库连接配置
    $config = [
        'host' => 'localhost',
        'dbname' => 'orderflow',
        'username' => 'root',
        'password' => 'root',
        'charset' => 'utf8mb4'
    ];
    
    // 创建PDO连接
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>检查订单相关表结构</h2>";
    
    // 检查所有表
    $tables_to_check = ['orders', 'order_items', 'restaurant_orders', 'restaurant_order_items'];
    
    foreach ($tables_to_check as $table) {
        echo "<h3>📋 检查表: {$table}</h3>";
        
        // 检查表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->fetch()) {
            echo "<p style='color: green;'>✅ 表 {$table} 存在</p>";
            
            // 显示表结构
            $stmt = $pdo->query("DESCRIBE {$table}");
            echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
            echo "<tr style='background-color: #f0f0f0;'><th>字段名</th><th>类型</th><th>允许NULL</th><th>键</th><th>默认值</th><th>注释</th></tr>";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $default = $row['Default'] ?? 'NULL';
                echo "<tr>";
                echo "<td>{$row['Field']}</td>";
                echo "<td>{$row['Type']}</td>";
                echo "<td>{$row['Null']}</td>";
                echo "<td>{$row['Key']}</td>";
                echo "<td>{$default}</td>";
                echo "<td>" . ($row['Comment'] ?? '') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // 显示记录数
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            echo "<p>📊 记录数: {$count}</p>";
            
        } else {
            echo "<p style='color: red;'>❌ 表 {$table} 不存在</p>";
            
            // 如果是orders表不存在，提供创建建议
            if ($table === 'orders') {
                echo "<div style='background-color: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>💡 建议创建orders表</h4>";
                echo "<p>可以基于restaurant_orders表创建orders表，或者直接使用restaurant_orders表。</p>";
                echo "</div>";
            }
            
            // 如果是order_items表不存在，提供创建建议
            if ($table === 'order_items') {
                echo "<div style='background-color: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>💡 建议创建order_items表</h4>";
                echo "<p>可以基于restaurant_order_items表创建order_items表，或者直接使用restaurant_order_items表。</p>";
                echo "</div>";
            }
        }
        
        echo "<hr>";
    }
    
    // 建议
    echo "<h3>🔧 建议的解决方案</h3>";
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px;'>";
    
    // 检查orders表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'orders'");
    $orders_exists = $stmt->fetch();
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'restaurant_orders'");
    $restaurant_orders_exists = $stmt->fetch();
    
    if (!$orders_exists && $restaurant_orders_exists) {
        echo "<h4>方案1: 重命名现有表（推荐）</h4>";
        echo "<p>将 restaurant_orders 重命名为 orders，将 restaurant_order_items 重命名为 order_items</p>";
        echo "<pre>";
        echo "RENAME TABLE restaurant_orders TO orders;\n";
        echo "RENAME TABLE restaurant_order_items TO order_items;";
        echo "</pre>";
        
        echo "<h4>方案2: 修改代码使用现有表</h4>";
        echo "<p>修改MobileOrderController代码，直接使用restaurant_orders和restaurant_order_items表</p>";
        
    } elseif ($orders_exists && !$restaurant_orders_exists) {
        echo "<h4>✅ 使用orders表</h4>";
        echo "<p>orders表已存在，代码会自动使用orders表</p>";
        
    } elseif ($orders_exists && $restaurant_orders_exists) {
        echo "<h4>⚠️ 两个表都存在</h4>";
        echo "<p>建议统一使用orders表，可以考虑迁移restaurant_orders的数据到orders表</p>";
        
    } else {
        echo "<h4>❌ 都不存在</h4>";
        echo "<p>需要创建orders表和order_items表</p>";
    }
    
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ 数据库操作失败</h3>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ 执行失败</h3>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
}
?>
