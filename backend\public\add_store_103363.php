<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=orderflow;charset=utf8mb4', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $pdo->exec("INSERT INTO shops (shop_id, shop_name, app_name, frontend, status) VALUES ('103363', '门店103363', 'OrderFlow', 'default', 1)");
    echo "门店103363添加成功";
} catch (Exception $e) {
    echo "错误: " . $e->getMessage();
}
?>
