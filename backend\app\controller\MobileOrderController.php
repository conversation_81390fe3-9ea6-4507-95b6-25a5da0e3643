<?php

namespace app\controller;

use think\facade\Db;
use think\Request;

class MobileOrderController extends BaseController
{
    /**
     * 创建订单 (手机端优化)
     */
    public function createOrder(Request $request)
    {
        try {
            $data = $request->param();
            
            // 验证必填字段
            $required_fields = ['items'];
            foreach ($required_fields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    return $this->error("缺少必填字段: {$field}");
                }
            }

            // 获取店铺信息
            $shop_code = $data['shop_code'] ?? '';
            $shop_name = '';

            if ($shop_code) {
                // 根据店铺编号查询店铺名称
                $store_info = Db::table('store_info')
                    ->where('code', $shop_code)
                    ->where('status', 1)
                    ->find();

                if ($store_info) {
                    $shop_name = $store_info['name'];
                } else {
                    return $this->error("店铺编号 {$shop_code} 不存在或已禁用");
                }
            }

            // 验证订单明细
            if (!is_array($data['items']) || empty($data['items'])) {
                return $this->error('订单明细不能为空');
            }

            // 验证手机号格式（如果提供了手机号且不为空）
            if (isset($data['customer_phone']) &&
                $data['customer_phone'] !== '' &&
                !preg_match('/^1[3-9]\d{9}$/', $data['customer_phone'])) {
                return $this->error('手机号格式不正确');
            }

            // 开启事务
            Db::startTrans();
            
            try {
                // 生成订单号
                $order_no = 'M' . date('YmdHis') . rand(1000, 9999);
                
                // 验证餐品并计算金额
                $subtotal_amount = 0;
                $validated_items = [];

                foreach ($data['items'] as $item) {
                    // 验证餐品是否存在且可用
                    $dish = Db::table('dishes')
                        ->where('id', $item['dish_id'])
                        ->where('status', 1)
                        ->where('is_available', 1)
                        ->find();

                    if (!$dish) {
                        throw new \Exception("餐品ID {$item['dish_id']} 不存在或已下架");
                    }

                    // 检查库存（如果库存为0则跳过库存检查，允许下单）
                    if ($dish['stock'] > 0 && $dish['stock'] < $item['quantity']) {
                        throw new \Exception("餐品 {$dish['name']} 库存不足");
                    }

                    // 使用数据库中的价格，不信任客户端传来的价格
                    $item_total = $dish['price'] * $item['quantity'];
                    $subtotal_amount += $item_total;

                    $validated_items[] = [
                        'dish_id' => $dish['id'],
                        'dish_name' => $dish['name'],
                        'unit_price' => $dish['price'],
                        'quantity' => (int)$item['quantity'],
                        'total_price' => $item_total,
                        'special_requirements' => $item['special_requirements'] ?? ''
                    ];
                }
                
                // 计算费用 - 重新计算总金额，不信任客户端传来的金额
                $service_fee = $data['service_fee'] ?? 0;
                $delivery_fee = $data['delivery_fee'] ?? 0;
                $discount_amount = $data['discount_amount'] ?? 0;
                $total_amount = $subtotal_amount + $service_fee + $delivery_fee - $discount_amount;
                
                // 插入订单主表
                $order_data = [
                    'order_no' => $order_no,
                    'customer_name' => $data['customer_name'] ?? '顾客',
                    'customer_phone' => $data['customer_phone'] ?? '',
                    'shop_code' => $shop_code,
                    'shop_name' => $shop_name,
                    'table_no' => $data['table_number'] ?? $data['table_no'] ?? '',
                    'order_type' => $data['dining_type'] ?? 'dine_in',
                    'subtotal' => $subtotal_amount,
                    'discount_amount' => $discount_amount,
                    'service_fee' => $service_fee,
                    'delivery_fee' => $delivery_fee,
                    'total_amount' => $total_amount,
                    'status' => 'pending',  // 初始状态为待确认
                    'payment_status' => 'paid',  // 标记为已支付（实际无需支付）
                    'payment_method' => 'cash',  // 使用现金作为默认支付方式
                    'special_requests' => $data['remark'] ?? $data['special_requirements'] ?? ''
                ];

                // 使用restaurant_orders表
                $order_id = Db::table('restaurant_orders')->insertGetId($order_data);
                
                // 插入订单明细并更新库存
                foreach ($validated_items as $item) {
                    $item_data = [
                        'order_id' => $order_id,
                        'dish_id' => $item['dish_id'],
                        'dish_name' => $item['dish_name'],
                        'dish_sku' => '',
                        'unit_price' => $item['unit_price'],
                        'quantity' => $item['quantity'],
                        'total_price' => $item['total_price'],
                        'special_requests' => $item['special_requirements'],
                        'status' => 'pending'
                    ];

                    // 使用restaurant_order_items表
                    Db::table('restaurant_order_items')->insert($item_data);
                    
                    // 更新餐品库存
                    Db::table('dishes')
                        ->where('id', $item['dish_id'])
                        ->dec('stock', $item['quantity']);
                }
                

                
                // 提交事务
                Db::commit();
                
                // 返回创建的订单信息
                $order = $this->getOrderDetail($order_id);
                
                return $this->success($order, '订单创建成功');
                
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }
            
        } catch (\Exception $e) {
            return $this->error('创建订单失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取订单详情 (手机端优化)
     */
    public function getOrderDetail($order_id = null, ?Request $request = null)
    {
        try {
            if ($order_id === null) {
                $order_id = $request->param('id');
            }
            
            if (!$order_id) {
                return $this->error('订单ID不能为空');
            }
            
            // 获取订单信息
            $order = Db::table('restaurant_orders')->where('id', $order_id)->find();
            
            if (!$order) {
                return $this->error('订单不存在', 404);
            }
            
            // 获取订单明细
            $items = Db::table('restaurant_order_items')
                ->alias('oi')
                ->leftJoin('dishes d', 'oi.dish_id = d.id')
                ->field([
                    'oi.*',
                    'd.main_image as dish_image',
                    'd.category_id',
                    'd.dish_type'
                ])
                ->where('oi.order_id', $order_id)
                ->order('oi.id asc')
                ->select()
                ->toArray();
            
            // 处理订单明细数据
            foreach ($items as &$item) {
                $item['id'] = (int)$item['id'];
                $item['dish_id'] = (int)$item['dish_id'];
                $item['unit_price'] = (float)$item['unit_price'];
                $item['quantity'] = (int)$item['quantity'];
                $item['total_price'] = (float)$item['total_price'];
                
                $item['status_text'] = $this->getItemStatusText($item['status']);
                
                // 处理图片URL
                if ($item['dish_image'] && !str_starts_with($item['dish_image'], 'http')) {
                    $item['dish_image'] = request()->domain() . $item['dish_image'];
                }
                
                // 移除敏感信息
                unset($item['created_at'], $item['updated_at']);
            }
            
            // 处理订单主数据
            $order['id'] = (int)$order['id'];
            $order['subtotal_amount'] = (float)$order['subtotal'];
            $order['discount_amount'] = (float)$order['discount_amount'];
            $order['service_fee'] = (float)$order['service_fee'];
            $order['delivery_fee'] = (float)$order['delivery_fee'];
            $order['total_amount'] = (float)$order['total_amount'];

            // 添加状态文本
            $order['status_text'] = $this->getStatusText($order['status']);
            $order['dining_type_text'] = $this->getDiningTypeText($order['order_type']);

            // 移除支付相关字段
            unset($order['payment_status'], $order['payment_method'], $order['paid_amount']);
            
            // 计算订单进度
            $order['progress'] = $this->calculateOrderProgress($order['status']);
            
            // 添加订单明细
            $order['items'] = $items;
            $order['item_count'] = count($items);
            $order['total_quantity'] = array_sum(array_column($items, 'quantity'));

            // 添加下单时间
            $order['order_time'] = $order['created_at'];

            // 移除敏感信息
            unset($order['created_at'], $order['updated_at']);
            
            if ($order_id === null) {
                return $this->success($order);
            } else {
                return $order;
            }
            
        } catch (\Exception $e) {
            if ($order_id === null) {
                return $this->error('获取订单详情失败: ' . $e->getMessage());
            } else {
                throw $e;
            }
        }
    }
    
    /**
     * 获取订单列表 (手机端优化)
     */
    public function getUserOrders(Request $request)
    {
        try {
            $customer_phone = $request->param('customer_phone');
            $customer_name = $request->param('customer_name');
            $table_no = $request->param('table_no');
            $order_no = $request->param('order_no');
            $status = $request->param('status');
            $page = $request->param('page', 1);
            $limit = $request->param('limit', 10);

            // 构建查询条件 - 支持多种查询方式
            $where = [];

            if ($customer_phone) {
                $where[] = ['customer_phone', '=', $customer_phone];
            }

            if ($customer_name) {
                $where[] = ['customer_name', 'like', '%' . $customer_name . '%'];
            }

            if ($table_no) {
                $where[] = ['table_no', '=', $table_no];
            }

            if ($order_no) {
                $where[] = ['order_no', 'like', '%' . $order_no . '%'];
            }

            if ($status) {
                $where[] = ['status', '=', $status];
            }

            // 如果没有任何查询条件，返回最近的订单
            if (empty($where)) {
                $where[] = ['created_at', '>=', date('Y-m-d', strtotime('-7 days'))];
            }
            
            // 查询订单列表
            $orders = Db::table('restaurant_orders')
                ->where($where)
                ->order('order_time desc')
                ->page($page, $limit)
                ->select()
                ->toArray();
            
            // 获取总数
            $total = Db::table('restaurant_orders')->where($where)->count();
            
            // 处理订单数据
            foreach ($orders as &$order) {
                // 基本数据处理
                $order['id'] = (int)$order['id'];
                $order['total_amount'] = (float)$order['total_amount'];

                // 状态文本
                $order['status_text'] = $this->getStatusText($order['status']);
                
                // 订单进度
                $order['progress'] = $this->calculateOrderProgress($order['status']);
                
                // 获取订单明细数量
                $item_stats = Db::table('restaurant_order_items')
                    ->where('order_id', $order['id'])
                    ->field('COUNT(*) as item_types, SUM(quantity) as total_quantity')
                    ->find();
                
                $order['item_types'] = (int)$item_stats['item_types'];
                $order['total_quantity'] = (int)$item_stats['total_quantity'];
                
                // 获取第一个餐品的图片作为订单缩略图
                $first_item = Db::table('restaurant_order_items')
                    ->alias('oi')
                    ->leftJoin('dishes d', 'oi.dish_id = d.id')
                    ->field('d.main_image')
                    ->where('oi.order_id', $order['id'])
                    ->order('oi.id asc')
                    ->find();
                
                if ($first_item && $first_item['main_image']) {
                    $order['thumbnail'] = str_starts_with($first_item['main_image'], 'http') 
                        ? $first_item['main_image'] 
                        : request()->domain() . $first_item['main_image'];
                } else {
                    $order['thumbnail'] = '';
                }

                // 添加下单时间
                $order['order_time'] = $order['created_at'];

                // 移除敏感信息和支付相关字段
                unset($order['created_at'], $order['updated_at'], $order['kitchen_memo'],
                      $order['payment_status'], $order['payment_method'], $order['paid_amount']);
            }
            
            return $this->success([
                'list' => $orders,
                'pagination' => [
                    'total' => $total,
                    'page' => (int)$page,
                    'limit' => (int)$limit,
                    'pages' => ceil($total / $limit)
                ]
            ]);
            
        } catch (\Exception $e) {
            return $this->error('获取订单列表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 取消订单 (手机端)
     */
    public function cancelOrder(Request $request)
    {
        try {
            $order_id = $request->param('id');
            $cancel_reason = $request->param('cancel_reason', '用户取消');
            
            if (!$order_id) {
                return $this->error('订单ID不能为空');
            }
            
            // 检查订单是否存在
            $order = Db::table('restaurant_orders')->where('id', $order_id)->find();
            if (!$order) {
                return $this->error('订单不存在', 404);
            }
            
            // 检查订单状态是否可以取消
            if (!in_array($order['status'], ['pending', 'confirmed', 'cooking'])) {
                return $this->error('当前订单状态不允许取消');
            }
            
            // 开启事务
            Db::startTrans();
            
            try {
                // 更新订单状态
                Db::table('restaurant_orders')
                    ->where('id', $order_id)
                    ->update([
                        'status' => 'cancelled'
                    ]);
                
                // 恢复库存
                $items = Db::table('restaurant_order_items')->where('order_id', $order_id)->select();
                foreach ($items as $item) {
                    Db::table('dishes')
                        ->where('id', $item['dish_id'])
                        ->inc('stock', $item['quantity']);
                }
                

                
                // 提交事务
                Db::commit();
                
                return $this->success([], '订单取消成功');
                
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
            
        } catch (\Exception $e) {
            return $this->error('取消订单失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取订单状态 (轮询用)
     */
    public function getOrderStatus(Request $request)
    {
        try {
            $order_id = $request->param('id');
            
            if (!$order_id) {
                return $this->error('订单ID不能为空');
            }
            
            $order = Db::table('restaurant_orders')
                ->field('id, order_no, status, total_amount')
                ->where('id', $order_id)
                ->find();

            if (!$order) {
                return $this->error('订单不存在', 404);
            }

            $order['status_text'] = $this->getStatusText($order['status']);
            $order['progress'] = $this->calculateOrderProgress($order['status']);
            
            return $this->success($order);
            
        } catch (\Exception $e) {
            return $this->error('获取订单状态失败: ' . $e->getMessage());
        }
    }
    
    // 辅助方法：计算订单进度
    private function calculateOrderProgress($status)
    {
        $progressMap = [
            'pending' => 10,      // 待确认
            'confirmed' => 20,    // 已确认
            'cooking' => 50,      // 制作中
            'ready' => 75,        // 待上菜
            'served' => 90,       // 已上菜
            'completed' => 100,   // 已完成
            'cancelled' => 0      // 已取消
        ];
        return $progressMap[$status] ?? 0;
    }
    
    // 辅助方法：获取状态文本
    private function getStatusText($status)
    {
        $statusMap = [
            'pending' => '待确认',
            'confirmed' => '已确认',
            'cooking' => '制作中',
            'ready' => '待上菜',
            'served' => '已上菜',
            'completed' => '已完成',
            'cancelled' => '已取消'
        ];
        return $statusMap[$status] ?? $status;
    }

    private function getDiningTypeText($type)
    {
        $typeMap = [
            'dine_in' => '堂食',
            'takeout' => '打包',
            'delivery' => '外卖'
        ];
        return $typeMap[$type] ?? $type;
    }



    private function getItemStatusText($status)
    {
        $statusMap = [
            'pending' => '待确认',
            'confirmed' => '已确认',
            'preparing' => '制作中',
            'ready' => '已完成',
            'served' => '已上菜',
            'cancelled' => '已取消'
        ];
        return $statusMap[$status] ?? $status;
    }


}
