#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加店铺字段到订单表的数据库迁移脚本
"""

import mysql.connector
import sys
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'database': 'orderflow',
    'charset': 'utf8mb4'
}

def connect_database():
    """连接数据库"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        return connection
    except mysql.connector.Error as err:
        print(f"❌ 数据库连接失败: {err}")
        sys.exit(1)

def check_column_exists(cursor, table_name, column_name):
    """检查列是否存在"""
    cursor.execute(f"""
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'orderflow' 
        AND TABLE_NAME = '{table_name}' 
        AND COLUMN_NAME = '{column_name}'
    """)
    return cursor.fetchone()[0] > 0

def add_shop_fields_to_orders(connection):
    """添加店铺字段到订单表"""
    cursor = connection.cursor()
    
    try:
        print("🔄 开始修改订单表结构...")
        
        # 检查并添加店铺编号字段
        if not check_column_exists(cursor, 'orders', 'shop_code'):
            print("📝 添加店铺编号字段 (shop_code)...")
            cursor.execute("""
                ALTER TABLE orders 
                ADD COLUMN shop_code VARCHAR(50) NULL COMMENT '店铺编号' 
                AFTER customer_phone
            """)
            print("✅ shop_code 字段添加成功")
        else:
            print("ℹ️ shop_code 字段已存在，跳过")
        
        # 检查并添加店铺名称字段
        if not check_column_exists(cursor, 'orders', 'shop_name'):
            print("📝 添加店铺名称字段 (shop_name)...")
            cursor.execute("""
                ALTER TABLE orders 
                ADD COLUMN shop_name VARCHAR(100) NULL COMMENT '店铺名称' 
                AFTER shop_code
            """)
            print("✅ shop_name 字段添加成功")
        else:
            print("ℹ️ shop_name 字段已存在，跳过")
        
        # 添加索引
        print("📝 添加索引...")
        try:
            cursor.execute("CREATE INDEX idx_shop_code ON orders(shop_code)")
            print("✅ shop_code 索引添加成功")
        except mysql.connector.Error as err:
            if "Duplicate key name" in str(err):
                print("ℹ️ shop_code 索引已存在，跳过")
            else:
                print(f"⚠️ 添加索引时出现警告: {err}")
        
        # 提交更改
        connection.commit()
        print("✅ 数据库结构修改完成")
        
    except mysql.connector.Error as err:
        print(f"❌ 修改订单表失败: {err}")
        connection.rollback()
        return False
    finally:
        cursor.close()
    
    return True

def create_store_info_table(connection):
    """创建店铺信息表（如果不存在）"""
    cursor = connection.cursor()
    
    try:
        print("🔄 检查店铺信息表...")
        
        # 检查表是否存在
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'orderflow' 
            AND TABLE_NAME = 'store_info'
        """)
        
        if cursor.fetchone()[0] == 0:
            print("📝 创建店铺信息表 (store_info)...")
            cursor.execute("""
                CREATE TABLE store_info (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    code VARCHAR(50) NOT NULL UNIQUE COMMENT '店铺编号',
                    name VARCHAR(100) NOT NULL COMMENT '店铺名称',
                    address VARCHAR(255) COMMENT '店铺地址',
                    phone VARCHAR(20) COMMENT '联系电话',
                    status TINYINT DEFAULT 1 COMMENT '状态: 0禁用, 1启用',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    
                    INDEX idx_code (code),
                    INDEX idx_status (status)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺信息表'
            """)
            print("✅ store_info 表创建成功")
            
            # 插入测试数据
            print("📝 插入测试店铺数据...")
            cursor.execute("""
                INSERT INTO store_info (code, name, address, phone, status) VALUES 
                ('SHOP001', '总店', '北京市朝阳区xxx路123号', '010-12345678', 1),
                ('SHOP002', '分店A', '北京市海淀区xxx路456号', '010-87654321', 1),
                ('SHOP003', '分店B', '北京市西城区xxx路789号', '010-11111111', 1),
                ('SHOP004', '测试店铺', '测试地址', '010-99999999', 0)
            """)
            print("✅ 测试店铺数据插入成功")
            
        else:
            print("ℹ️ store_info 表已存在，跳过创建")
        
        connection.commit()
        
    except mysql.connector.Error as err:
        print(f"❌ 创建店铺信息表失败: {err}")
        connection.rollback()
        return False
    finally:
        cursor.close()
    
    return True

def verify_changes(connection):
    """验证修改结果"""
    cursor = connection.cursor()
    
    try:
        print("🔍 验证修改结果...")
        
        # 检查订单表结构
        cursor.execute("DESCRIBE orders")
        columns = cursor.fetchall()
        
        print("\n📋 当前订单表结构:")
        print("字段名\t\t类型\t\t\t空值\t键\t默认值\t备注")
        print("-" * 80)
        for column in columns:
            field, type_, null, key, default, extra = column
            print(f"{field:<15} {type_:<20} {null:<8} {key:<8} {str(default):<10} {extra}")
        
        # 检查店铺信息表数据
        cursor.execute("SELECT * FROM store_info LIMIT 5")
        stores = cursor.fetchall()
        
        print("\n🏪 店铺信息表数据:")
        print("ID\t编号\t\t名称\t\t状态")
        print("-" * 50)
        for store in stores:
            id_, code, name, address, phone, status, created_at, updated_at = store
            status_text = "启用" if status == 1 else "禁用"
            print(f"{id_}\t{code}\t\t{name}\t\t{status_text}")
        
        print(f"\n✅ 验证完成！订单表已添加店铺相关字段")
        
    except mysql.connector.Error as err:
        print(f"❌ 验证失败: {err}")
        return False
    finally:
        cursor.close()
    
    return True

def main():
    """主函数"""
    print("🚀 开始执行订单表店铺字段添加脚本")
    print(f"⏰ 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)
    
    # 连接数据库
    connection = connect_database()
    
    try:
        # 创建店铺信息表
        if not create_store_info_table(connection):
            print("❌ 创建店铺信息表失败，终止执行")
            return
        
        # 修改订单表
        if not add_shop_fields_to_orders(connection):
            print("❌ 修改订单表失败，终止执行")
            return
        
        # 验证修改结果
        if not verify_changes(connection):
            print("❌ 验证修改结果失败")
            return
        
        print("\n" + "=" * 60)
        print("🎉 所有操作执行成功！")
        print("📝 已添加字段:")
        print("   - orders.shop_code (店铺编号)")
        print("   - orders.shop_name (店铺名称)")
        print("🏪 已创建店铺信息表: store_info")
        print("📊 已添加测试店铺数据")
        print("=" * 60)
        
    finally:
        connection.close()
        print("🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()
