import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![export](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNODgwIDkxMkgxNDRjLTE3LjcgMC0zMi0xNC4zLTMyLTMyVjE0NGMwLTE3LjcgMTQuMy0zMiAzMi0zMmgzNjBjNC40IDAgOCAzLjYgOCA4djU2YzAgNC40LTMuNiA4LTggOEgxODR2NjU2aDY1NlY1MjBjMC00LjQgMy42LTggOC04aDU2YzQuNCAwIDggMy42IDggOHYzNjBjMCAxNy43LTE0LjMgMzItMzIgMzJ6TTc3MC44NyAxOTkuMTNsLTUyLjItNTIuMmE4LjAxIDguMDEgMCAwMTQuNy0xMy42bDE3OS40LTIxYzUuMS0uNiA5LjUgMy43IDguOSA4LjlsLTIxIDE3OS40Yy0uOCA2LjYtOC45IDkuNC0xMy42IDQuN2wtNTIuNC01Mi40LTI1Ni4yIDI1Ni4yYTguMDMgOC4wMyAwIDAxLTExLjMgMGwtNDIuNC00Mi40YTguMDMgOC4wMyAwIDAxMC0xMS4zbDI1Ni4xLTI1Ni4zeiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
