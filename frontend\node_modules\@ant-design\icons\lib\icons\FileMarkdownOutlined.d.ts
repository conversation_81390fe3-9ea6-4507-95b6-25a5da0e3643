import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![file-markdown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC42TDYzOS40IDczLjRjLTYtNi0xNC4xLTkuNC0yMi42LTkuNEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzExLjNjMC04LjUtMy40LTE2LjctOS40LTIyLjd6TTc5MC4yIDMyNkg2MDJWMTM3LjhMNzkwLjIgMzI2em0xLjggNTYySDIzMlYxMzZoMzAydjIxNmE0MiA0MiAwIDAwNDIgNDJoMjE2djQ5NHpNNDI5IDQ4MS4yYy0xLjktNC40LTYuMi03LjItMTEtNy4yaC0zNWMtNi42IDAtMTIgNS40LTEyIDEydjI3MmMwIDYuNiA1LjQgMTIgMTIgMTJoMjcuMWM2LjYgMCAxMi01LjQgMTItMTJWNTgyLjFsNjYuOCAxNTAuMmExMiAxMiAwIDAwMTEgNy4xSDUyNGM0LjcgMCA5LTIuOCAxMS03LjFsNjYuOC0xNTAuNlY3NThjMCA2LjYgNS40IDEyIDEyIDEySDY0MWM2LjYgMCAxMi01LjQgMTItMTJWNDg2YzAtNi42LTUuNC0xMi0xMi0xMmgtMzQuN2MtNC44IDAtOS4xIDIuOC0xMSA3LjJsLTgzLjEgMTkxLTgzLjItMTkxeiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
