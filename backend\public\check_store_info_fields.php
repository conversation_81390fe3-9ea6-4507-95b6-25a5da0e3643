<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=orderflow;charset=utf8mb4', 'root', 'root');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("DESCRIBE store_info");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo $row['Field'] . " - " . $row['Type'] . "<br>";
    }
} catch (Exception $e) {
    echo "错误: " . $e->getMessage();
}
?>
