<?php
// 为订单表添加店铺相关字段的数据库迁移脚本

try {
    // 数据库连接配置
    $config = [
        'host' => 'localhost',
        'dbname' => 'orderflow',
        'username' => 'root',
        'password' => 'root',
        'charset' => 'utf8mb4'
    ];
    
    // 创建PDO连接
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>为订单表添加店铺字段</h2>";
    echo "<p>开始修改数据库结构...</p>";
    
    // 检查orders表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'orders'");
    if (!$stmt->fetch()) {
        echo "<p style='color: red;'>❌ orders表不存在，请先创建orders表</p>";
        exit;
    }
    
    // 检查当前orders表结构
    echo "<h3>📋 检查orders表当前结构</h3>";
    $stmt = $pdo->query("DESCRIBE orders");
    $columns = [];
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'><th>字段名</th><th>类型</th><th>允许NULL</th><th>默认值</th><th>注释</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $columns[] = $row['Field'];
        $default = $row['Default'] ?? 'NULL';
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$default}</td>";
        echo "<td>" . ($row['Comment'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 检查是否已经有店铺相关字段
    $hasShopCode = in_array('shop_code', $columns);
    $hasShopName = in_array('shop_name', $columns);
    
    echo "<h3>🔧 开始添加店铺字段</h3>";
    
    if ($hasShopCode && $hasShopName) {
        echo "<p style='color: orange;'>ℹ️ 店铺字段已存在，跳过添加</p>";
    } else {
        // 添加店铺编号字段
        if (!$hasShopCode) {
            echo "<p>📝 添加店铺编号字段 (shop_code)...</p>";
            $pdo->exec("
                ALTER TABLE orders 
                ADD COLUMN shop_code VARCHAR(50) DEFAULT NULL COMMENT '店铺编号' 
                AFTER customer_phone
            ");
            echo "<p style='color: green;'>✅ shop_code字段添加成功</p>";
        } else {
            echo "<p style='color: orange;'>ℹ️ shop_code字段已存在，跳过</p>";
        }
        
        // 添加店铺名称字段
        if (!$hasShopName) {
            echo "<p>📝 添加店铺名称字段 (shop_name)...</p>";
            $pdo->exec("
                ALTER TABLE orders 
                ADD COLUMN shop_name VARCHAR(100) DEFAULT NULL COMMENT '店铺名称' 
                AFTER shop_code
            ");
            echo "<p style='color: green;'>✅ shop_name字段添加成功</p>";
        } else {
            echo "<p style='color: orange;'>ℹ️ shop_name字段已存在，跳过</p>";
        }
        
        // 添加索引
        echo "<p>📝 添加索引...</p>";
        try {
            $pdo->exec("ALTER TABLE orders ADD INDEX idx_shop_code (shop_code)");
            echo "<p style='color: green;'>✅ shop_code索引添加成功</p>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<p style='color: orange;'>ℹ️ shop_code索引已存在，跳过</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ 添加索引时出现警告: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // 检查store_info表是否存在
    echo "<h3>🏪 检查店铺信息表</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'store_info'");
    if (!$stmt->fetch()) {
        echo "<p>⚠️ store_info表不存在，创建店铺信息表...</p>";
        $pdo->exec("
            CREATE TABLE store_info (
                id INT PRIMARY KEY AUTO_INCREMENT,
                code VARCHAR(50) NOT NULL UNIQUE COMMENT '店铺编号',
                name VARCHAR(100) NOT NULL COMMENT '店铺名称',
                address VARCHAR(255) COMMENT '店铺地址',
                phone VARCHAR(20) COMMENT '联系电话',
                status TINYINT DEFAULT 1 COMMENT '状态: 0禁用, 1启用',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                
                INDEX idx_code (code),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺信息表'
        ");
        echo "<p style='color: green;'>✅ store_info表创建成功</p>";
        
        // 插入测试数据
        echo "<p>📝 插入测试店铺数据...</p>";
        $pdo->exec("
            INSERT INTO store_info (code, name, address, phone, status) VALUES 
            ('SHOP001', '总店', '北京市朝阳区xxx路123号', '010-12345678', 1),
            ('SHOP002', '分店A', '北京市海淀区xxx路456号', '010-87654321', 1),
            ('SHOP003', '分店B', '北京市西城区xxx路789号', '010-11111111', 1),
            ('TEST001', '测试店铺', '测试地址', '000-00000000', 0)
        ");
        echo "<p style='color: green;'>✅ 测试店铺数据插入成功</p>";
    } else {
        echo "<p style='color: green;'>✅ store_info表已存在</p>";
    }
    
    // 验证修改结果
    echo "<h3>📊 验证修改结果</h3>";
    
    // 显示修改后的orders表结构
    echo "<h4>orders表结构（修改后）:</h4>";
    $stmt = $pdo->query("DESCRIBE orders");
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'><th>字段名</th><th>类型</th><th>允许NULL</th><th>默认值</th><th>注释</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $default = $row['Default'] ?? 'NULL';
        $bgColor = in_array($row['Field'], ['shop_code', 'shop_name']) ? 'background-color: #e6ffe6;' : '';
        echo "<tr style='{$bgColor}'>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$default}</td>";
        echo "<td>" . ($row['Comment'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 显示store_info表数据
    echo "<h4>store_info表数据:</h4>";
    $stmt = $pdo->query("SELECT * FROM store_info ORDER BY id");
    $storeData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($storeData) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'><th>ID</th><th>编号</th><th>名称</th><th>地址</th><th>电话</th><th>状态</th><th>创建时间</th></tr>";
        foreach ($storeData as $row) {
            $statusText = $row['status'] == 1 ? '启用' : '禁用';
            $statusColor = $row['status'] == 1 ? 'green' : 'red';
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td style='font-family: monospace;'>{$row['code']}</td>";
            echo "<td>{$row['name']}</td>";
            echo "<td>{$row['address']}</td>";
            echo "<td>{$row['phone']}</td>";
            echo "<td style='color: {$statusColor};'>{$statusText}</td>";
            echo "<td>{$row['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3 style='color: green;'>🎉 数据库修改完成！</h3>";
    echo "<div style='background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 修改内容总结:</h4>";
    echo "<ul>";
    echo "<li>✅ 在orders表中添加了shop_code字段（店铺编号）</li>";
    echo "<li>✅ 在orders表中添加了shop_name字段（店铺名称）</li>";
    echo "<li>✅ 为shop_code字段添加了索引</li>";
    echo "<li>✅ 确保store_info表存在并包含测试数据</li>";
    echo "</ul>";
    echo "<h4>🔧 接下来需要修改的代码:</h4>";
    echo "<ul>";
    echo "<li>📱 修改MobileOrderController，在创建订单时接收shop_code参数</li>";
    echo "<li>🔍 根据shop_code查询store_info表获取shop_name</li>";
    echo "<li>💾 将shop_code和shop_name保存到orders表</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ 数据库操作失败</h3>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>请检查数据库连接配置和权限。</p>";
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ 执行失败</h3>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
}
?>
