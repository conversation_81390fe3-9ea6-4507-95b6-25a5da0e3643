<?php
// 为阿奇索平台添加店铺信息

try {
    // 数据库连接配置
    $config = [
        'host' => 'localhost',
        'dbname' => 'orderflow',
        'username' => 'root',
        'password' => 'root',
        'charset' => 'utf8mb4'
    ];
    
    // 创建PDO连接
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>为阿奇索平台添加店铺信息</h2>";
    echo "<p>开始添加阿奇索相关店铺...</p>";
    
    // 阿奇索平台店铺数据
    $agiso_stores = [
        [
            'code' => 'AGISO_TBALDS',
            'name' => '淘宝ALDS店铺',
            'address' => '阿奇索-淘宝ALDS平台',
            'phone' => '400-AGISO-TB',
            'status' => 1
        ],
        [
            'code' => 'AGISO_ALDSIDLE',
            'name' => '闲鱼店铺',
            'address' => '阿奇索-闲鱼平台',
            'phone' => '400-AGISO-XY',
            'status' => 1
        ],
        [
            'code' => 'AGISO_PDDALDS',
            'name' => '拼多多店铺',
            'address' => '阿奇索-拼多多平台',
            'phone' => '400-AGISO-PDD',
            'status' => 1
        ],
        [
            'code' => 'AGISO_DEFAULT',
            'name' => '阿奇索默认店铺',
            'address' => '阿奇索-默认平台',
            'phone' => '400-AGISO-DEF',
            'status' => 1
        ]
    ];
    
    echo "<h3>🏪 添加阿奇索店铺信息</h3>";
    
    $added_count = 0;
    $skipped_count = 0;
    
    foreach ($agiso_stores as $store) {
        // 检查店铺是否已存在
        $stmt = $pdo->prepare("SELECT id FROM store_info WHERE code = ?");
        $stmt->execute([$store['code']]);
        
        if ($stmt->fetch()) {
            echo "<p style='color: orange;'>ℹ️ 店铺 {$store['code']} ({$store['name']}) 已存在，跳过</p>";
            $skipped_count++;
        } else {
            // 插入新店铺
            $stmt = $pdo->prepare("
                INSERT INTO store_info (code, name, address, phone, status) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $store['code'],
                $store['name'],
                $store['address'],
                $store['phone'],
                $store['status']
            ]);
            echo "<p style='color: green;'>✅ 添加店铺 {$store['code']} ({$store['name']}) 成功</p>";
            $added_count++;
        }
    }
    
    echo "<h3>📊 操作结果统计</h3>";
    echo "<p>✅ 新增店铺: {$added_count} 个</p>";
    echo "<p>ℹ️ 跳过店铺: {$skipped_count} 个</p>";
    
    // 显示所有店铺信息
    echo "<h3>🏪 当前所有店铺信息</h3>";
    $stmt = $pdo->query("SELECT * FROM store_info ORDER BY id");
    $all_stores = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($all_stores) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
        echo "<tr style='background-color: #f0f0f0;'><th>ID</th><th>编号</th><th>名称</th><th>地址</th><th>电话</th><th>状态</th><th>创建时间</th></tr>";
        foreach ($all_stores as $row) {
            $statusText = $row['status'] == 1 ? '启用' : '禁用';
            $statusColor = $row['status'] == 1 ? 'green' : 'red';
            $bgColor = strpos($row['code'], 'AGISO_') === 0 ? 'background-color: #e6f3ff;' : '';
            echo "<tr style='{$bgColor}'>";
            echo "<td>{$row['id']}</td>";
            echo "<td style='font-family: monospace;'>{$row['code']}</td>";
            echo "<td>{$row['name']}</td>";
            echo "<td>{$row['address']}</td>";
            echo "<td>{$row['phone']}</td>";
            echo "<td style='color: {$statusColor};'>{$statusText}</td>";
            echo "<td>{$row['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p style='color: #666; font-size: 12px;'>💡 蓝色背景的是阿奇索相关店铺</p>";
    }
    
    echo "<h3 style='color: green;'>🎉 阿奇索店铺信息添加完成！</h3>";
    echo "<div style='background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📝 阿奇索店铺编号说明:</h4>";
    echo "<ul>";
    echo "<li><strong>AGISO_TBALDS</strong> - 淘宝ALDS平台店铺</li>";
    echo "<li><strong>AGISO_ALDSIDLE</strong> - 闲鱼平台店铺</li>";
    echo "<li><strong>AGISO_PDDALDS</strong> - 拼多多平台店铺</li>";
    echo "<li><strong>AGISO_DEFAULT</strong> - 阿奇索默认店铺</li>";
    echo "</ul>";
    echo "<h4>🔧 自动映射规则:</h4>";
    echo "<ul>";
    echo "<li>当阿奇索推送订单时，系统会根据 fromPlatform 参数自动映射到对应店铺</li>";
    echo "<li>例如: fromPlatform=TbAlds → shop_code=AGISO_TBALDS</li>";
    echo "<li>系统会自动查询店铺名称并保存到订单中</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ 数据库操作失败</h3>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>请检查数据库连接配置和权限。</p>";
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ 执行失败</h3>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
}
?>
